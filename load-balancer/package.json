{"name": "@stream-docmq/load-balancer", "version": "1.0.0", "description": "Distributed load balancer microservice for monitoring and balancing DocMQ workers", "main": "distributed-load-balancer.js", "type": "module", "scripts": {"start": "bun distributed-load-balancer.js", "monitor": "bun distributed-load-balancer.js --health-interval=15 --rebalance-interval=60", "dev": "bun distributed-load-balancer.js --mode=dev", "prod": "bun distributed-load-balancer.js --mode=prod", "test-api": "./test-api.sh", "api-health": "curl http://localhost:3002/api/health", "api-stats": "curl http://localhost:3002/api/load-balancer/stats", "api-workers": "curl http://localhost:3002/api/workers"}, "author": "Your Name", "license": "MIT", "dependencies": {"chalk": "^5.0.0", "dotenv": "^16.3.1", "hono": "^4.7.10", "lru-cache": "^10.0.0", "mongodb": "^6.0.0", "os": "^0.1.2"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/stream-docmq.git", "directory": "load-balancer"}, "bugs": {"url": "https://github.com/your-username/stream-docmq/issues"}, "homepage": "https://github.com/your-username/stream-docmq/tree/main/load-balancer#readme"}