import {MongoClient} from "mongodb";
import {docmqConfig} from "./docmq-config.js";


let cachedClient = null;
let cachedDb = null;
const uri = docmqConfig.driver.options.mongo.uri;

export const mongoConnect = async (databaseName = 'conversion_finder') => {
    if (cachedClient && cachedDb) {
        return {client: cachedClient, db: cachedDb};
    }
    console.log('uri: ',uri)

    const client = new MongoClient(uri);
    try {
        await client.connect();
        const db = client.db(databaseName);
        cachedClient = client;
        cachedDb = db;
        return {client, db};
    } catch (error) {
        return {client: null, db: null};
    }
};
