import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSON } from "hono/pretty-json";
import { serve } from "@hono/node-server";
import os from 'os';

export class HttpServer {
    constructor(loadBalancer = null) {
        this.app = new Hono().basePath("/api");
        this.port = process.env.PORT || 3002;
        this.isRunning = false;
        this.server = null;
        this.loadBalancer = loadBalancer;

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        this.app.use("/*", cors({
            origin: "*",
            allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allowHeaders: ["Content-Type", "Authorization"],
        }));

        this.app.use("/*", logger());
        this.app.use("/*", prettyJSON());
    }

    setupRoutes() {
        // Health check básico
        this.app.get("/health", (c) => {
            return c.json({
                status: "ok",
                service: "stream-docmq-load-balancer",
                version: process.env.VERSION || "1.0.0",
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || "development"
            });
        });

        // Información del load balancer
        this.app.get("/load-balancer/info", (c) => {
            const lbInfo = {
                instanceId: this.loadBalancer?.instanceId || "unknown",
                isRunning: this.loadBalancer?.isRunning || false,
                startTime: this.loadBalancer?.startTime || null,
                healthCheckInterval: this.loadBalancer?.healthCheckInterval || 30000,
                rebalanceInterval: this.loadBalancer?.rebalanceInterval || 120000,
                serverInfo: {
                    hostname: os.hostname(),
                    platform: os.platform(),
                    cpus: os.cpus().length,
                    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024),
                    freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024),
                    loadAverage: os.loadavg(),
                    nodeVersion: process.version,
                    pid: process.pid
                }
            };

            return c.json(lbInfo);
        });

        // Estadísticas del load balancer
        this.app.get("/load-balancer/stats", (c) => {
            if (!this.loadBalancer) {
                return c.json({ error: "Load balancer not initialized" }, 503);
            }

            const uptime = this.loadBalancer.startTime ?
                Math.floor((Date.now() - this.loadBalancer.startTime) / 1000) : 0;

            const stats = {
                uptime: uptime,
                startTime: this.loadBalancer.startTime,
                isRunning: this.loadBalancer.isRunning || false,
                healthChecksPerformed: this.loadBalancer.healthChecksPerformed || 0,
                rebalancingOperations: this.loadBalancer.rebalancingOperations || 0,
                lastHealthCheck: this.loadBalancer.lastHealthCheck || null,
                lastRebalancing: this.loadBalancer.lastRebalancing || null
            };

            return c.json(stats);
        });

        // Workers registrados
        this.app.get("/workers", async (c) => {
            if (!this.loadBalancer || !this.loadBalancer.instanceStatsCollection) {
                return c.json({ error: "Load balancer not initialized" }, 503);
            }

            try {
                const workers = await this.loadBalancer.instanceStatsCollection
                    .find({})
                    .sort({ lastHeartbeat: -1 })
                    .toArray();

                const workersInfo = workers.map(worker => ({
                    instanceId: worker.instanceId,
                    concurrency: worker.concurrency,
                    activeJobs: worker.activeJobs,
                    processedJobs: worker.processedJobs,
                    utilization: worker.utilization,
                    jobsPerMinute: worker.jobsPerMinute,
                    lastHeartbeat: worker.lastHeartbeat,
                    serverInfo: worker.serverInfo,
                    isActive: (Date.now() - new Date(worker.lastHeartbeat).getTime()) < 90000
                }));

                return c.json({
                    workers: workersInfo,
                    totalWorkers: workersInfo.length,
                    activeWorkers: workersInfo.filter(w => w.isActive).length,
                    totalConcurrency: workersInfo.reduce((sum, w) => sum + w.concurrency, 0),
                    totalActiveJobs: workersInfo.reduce((sum, w) => sum + w.activeJobs, 0)
                });
            } catch (error) {
                return c.json({ error: "Failed to fetch workers", details: error.message }, 500);
            }
        });

        // Estado del load balancer (combinado)
        this.app.get("/load-balancer/status", (c) => {
            if (!this.loadBalancer) {
                return c.json({ error: "Load balancer not initialized" }, 503);
            }

            const uptime = this.loadBalancer.startTime ?
                Math.floor((Date.now() - this.loadBalancer.startTime) / 1000) : 0;

            const status = {
                loadBalancer: {
                    instanceId: this.loadBalancer.instanceId,
                    isRunning: this.loadBalancer.isRunning || false,
                    uptime: uptime
                },
                stats: {
                    healthChecksPerformed: this.loadBalancer.healthChecksPerformed || 0,
                    rebalancingOperations: this.loadBalancer.rebalancingOperations || 0,
                    lastHealthCheck: this.loadBalancer.lastHealthCheck,
                    lastRebalancing: this.loadBalancer.lastRebalancing
                },
                system: {
                    hostname: os.hostname(),
                    cpus: os.cpus().length,
                    freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024),
                    loadAverage: os.loadavg()[0]
                }
            };

            return c.json(status);
        });

        // Métricas para Prometheus/Grafana
        this.app.get("/metrics", (c) => {
            if (!this.loadBalancer) {
                return c.text("# Load balancer not initialized\n");
            }

            const uptime = this.loadBalancer.startTime ?
                Math.floor((Date.now() - this.loadBalancer.startTime) / 1000) : 0;

            const metrics = `# HELP load_balancer_uptime_seconds Load balancer uptime in seconds
# TYPE load_balancer_uptime_seconds gauge
load_balancer_uptime_seconds{instance_id="${this.loadBalancer.instanceId}"} ${uptime}

# HELP load_balancer_health_checks_total Total number of health checks performed
# TYPE load_balancer_health_checks_total counter
load_balancer_health_checks_total{instance_id="${this.loadBalancer.instanceId}"} ${this.loadBalancer.healthChecksPerformed || 0}

# HELP load_balancer_rebalancing_operations_total Total number of rebalancing operations
# TYPE load_balancer_rebalancing_operations_total counter
load_balancer_rebalancing_operations_total{instance_id="${this.loadBalancer.instanceId}"} ${this.loadBalancer.rebalancingOperations || 0}

# HELP load_balancer_running Load balancer running status
# TYPE load_balancer_running gauge
load_balancer_running{instance_id="${this.loadBalancer.instanceId}"} ${this.loadBalancer.isRunning ? 1 : 0}

# HELP system_memory_free_bytes Free system memory in bytes
# TYPE system_memory_free_bytes gauge
system_memory_free_bytes{service="load_balancer"} ${os.freemem()}
`;

            return c.text(metrics);
        });

        // Lista de endpoints disponibles
        this.app.get("/", (c) => {
            const endpoints = {
                service: "stream-docmq-load-balancer",
                version: process.env.VERSION || "1.0.0",
                endpoints: {
                    health: "GET /api/health",
                    loadBalancerInfo: "GET /api/load-balancer/info",
                    loadBalancerStats: "GET /api/load-balancer/stats",
                    loadBalancerStatus: "GET /api/load-balancer/status",
                    workers: "GET /api/workers",
                    metrics: "GET /api/metrics"
                },
                documentation: "https://github.com/your-username/stream-docmq"
            };

            return c.json(endpoints);
        });
    }

    async start() {
        if (this.isRunning) {
            console.warn("⚠️  HTTP server is already running");
            return;
        }

        try {
            this.isRunning = true;

            this.server = serve({
                fetch: this.app.fetch,
                port: this.port,
            });

            console.log(`🌐 Load Balancer API server started at http://localhost:${this.port}/api`);
            console.log(`📊 Health check: http://localhost:${this.port}/api/health`);
            console.log(`📈 Load Balancer stats: http://localhost:${this.port}/api/load-balancer/stats`);
            console.log(`👷 Workers: http://localhost:${this.port}/api/workers`);
            console.log(`📋 All endpoints: http://localhost:${this.port}/api`);

            return {
                port: this.port,
                fetch: this.app.fetch,
                server: this.server
            };
        } catch (error) {
            console.error("❌ Failed to start HTTP server:", error);
            this.isRunning = false;
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            console.warn("⚠️  HTTP server is not running");
            return;
        }

        try {
            if (this.server) {
                this.server.close();
                console.log("🛑 HTTP server stopped");
            }
            this.isRunning = false;
        } catch (error) {
            console.error("❌ Error stopping HTTP server:", error);
            throw error;
        }
    }

    setLoadBalancer(loadBalancer) {
        this.loadBalancer = loadBalancer;
    }

    getServerConfig() {
        return {
            port: this.port,
            fetch: this.app.fetch,
        };
    }
}

export default HttpServer
