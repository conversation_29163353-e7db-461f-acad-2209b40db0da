# 🌐 Load Balancer Microservice

Microservicio load balancer distribuido que monitorea múltiples workers, detecta desequilibrios y genera recomendaciones automáticas.

## 🚀 Características

- ✅ **Monitoreo distribuido**: Rastrea múltiples workers en tiempo real
- ✅ **Health checks automáticos**: Detecta workers caídos
- ✅ **Rebalancing inteligente**: Identifica desequilibrios de carga
- ✅ **Métricas históricas**: Almacena datos para análisis
- ✅ **Recomendaciones automáticas**: Sugiere acciones de optimización

## 📦 Instalación

```bash
cd load-balancer
npm install
```

## ⚙️ Configuración

El load balancer usa la configuración compartida en `../shared/docmq-config.js`. Configurar:

- **MongoDB URI**: Para almacenar métricas y estado
- **Intervalos de monitoreo**: Health checks y rebalancing
- **Umbrales de utilización**: Para detectar desequilibrios

## 🚀 Uso

### **Producción:**
```bash
npm start
```

### **Desarrollo:**
```bash
npm run dev
```

### **Monitoreo intensivo:**
```bash
npm run monitor
# Health checks cada 15s, rebalancing cada 60s
```

### **Personalizado:**
```bash
node distributed-load-balancer.js --instance=prod-lb --health-interval=30 --rebalance-interval=120
```

## 📊 Parámetros

- `--instance=<id>`: ID del load balancer (default: auto-generado)
- `--health-interval=<ms>`: Intervalo de health checks (default: 30000ms)
- `--rebalance-interval=<ms>`: Intervalo de rebalancing (default: 120000ms)

## 🔄 Funcionamiento

### **1. Health Checks (cada 30s):**
- Verifica workers activos vs inactivos
- Detecta workers que no reportan heartbeat
- Limpia registros de workers caídos

### **2. Rebalancing (cada 2min):**
- Analiza utilización de cada worker
- Detecta desequilibrios significativos
- Genera recomendaciones automáticas

### **3. Métricas (cada 1min):**
- Recolecta estadísticas globales
- Almacena historial para análisis
- Mantiene solo últimas 24 horas

## 📊 Datos Monitoreados

### **Por Worker:**
```javascript
{
  instanceId: 'lb-worker-server-1',
  concurrency: 16,
  activeJobs: 8,
  processedJobs: 156,
  utilization: 0.5,        // 8/16 = 50%
  jobsPerMinute: 22,
  lastHeartbeat: Date,
  serverInfo: {
    hostname: 'server-1',
    cpus: 8,
    memory: '16GB'
  }
}
```

### **Globales:**
```javascript
{
  totalInstances: 3,
  totalConcurrency: 48,
  totalActiveJobs: 18,
  avgUtilization: 0.375,   // 18/48 = 37.5%
  totalProcessedJobs: 1250
}
```

## 🎯 Recomendaciones Automáticas

### **Sobrecarga detectada:**
```javascript
{
  type: 'rebalancing_recommendation',
  overutilized: [
    { instanceId: 'server-1', utilization: 0.95, excessLoad: 3 }
  ],
  underutilized: [
    { instanceId: 'server-3', utilization: 0.2, availableCapacity: 10 }
  ],
  recommendation: 'Consider redistributing load or scaling instances'
}
```

### **Acciones sugeridas:**
- **Escalar horizontalmente**: Agregar más workers
- **Escalar verticalmente**: Aumentar concurrencia
- **Redistribuir**: Mover workers entre servidores
- **Optimizar**: Revisar rendimiento de workers lentos

## 🔧 Variables de Entorno

```bash
# MongoDB
MONGODB_URI=mongodb://localhost:27017/docmq
MONGODB_DB=queue_system

# Load Balancer
LB_HEALTH_INTERVAL=30000
LB_REBALANCE_INTERVAL=120000
LB_INSTANCE_TIMEOUT=90000
LB_TARGET_UTILIZATION=0.8
```

## 🐳 Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["npm", "start"]
```

## 📈 Escalabilidad

### **Múltiples Load Balancers:**
```bash
# Load Balancer Principal
npm start

# Load Balancer Backup (solo monitoreo)
npm run dev
```

**Nota:** Solo uno debe estar activo para evitar conflictos en recomendaciones.

## 📊 Monitoreo en Tiempo Real

```
📊 ESTADO ACTUAL DEL LOAD BALANCER
----------------------------------------
🌐 Instancias activas: 3
🔄 Concurrencia total: 48
📊 Jobs activos: 18
📈 Jobs procesados: 1,250
⚖️  Utilización promedio: 37.5%

📋 INSTANCIAS:
   1. 🟢 lb-worker-server-1
      Concurrencia: 16, Activos: 8
      Utilización: 50.0%, Jobs/min: 22
   2. 🟡 lb-worker-server-2
      Concurrencia: 20, Activos: 16
      Utilización: 80.0%, Jobs/min: 28
   3. 🟢 lb-worker-server-3
      Concurrencia: 12, Activos: 2
      Utilización: 16.7%, Jobs/min: 8
```

## 🚨 Troubleshooting

### **No detecta workers:**
1. Verificar que workers estén ejecutándose
2. Comprobar conexión a MongoDB
3. Verificar que workers se registren correctamente

### **Recomendaciones incorrectas:**
1. Ajustar umbrales de utilización
2. Verificar métricas de workers
3. Revisar intervalos de monitoreo

### **Métricas inconsistentes:**
1. Verificar sincronización de tiempo entre servidores
2. Comprobar latencia de red
3. Revisar configuración de heartbeat

## 📝 Logs

```
🌐 LOAD BALANCER DISTRIBUIDO
========================================
📋 Load Balancer ID: lb-prod
🗄️  Base de datos: queue_system
⏱️  Health check: cada 30s
⚖️  Rebalancing: cada 120s
🎯 Target utilization: 80%

🔍 Realizando health check...
📊 Instancias activas: 3

⚖️  Realizando rebalancing de carga...
🔄 Ejecutando rebalancing...
📊 Recomendación creada: 1 subutilizadas, 1 sobreutilizadas

📊 Quick Stats: 3 workers, 2 system processes running
```

## 🔗 Dependencias

- **MongoDB**: Almacenamiento de métricas y estado
- **Chalk**: Logs con colores
- **Shared**: Configuraciones compartidas

## 🔄 Integración

El load balancer se integra con:

1. **Workers**: Recibe registros y estadísticas automáticamente
2. **MongoDB**: Almacena métricas y recomendaciones
3. **Dashboards externos**: Proporciona datos para visualización
4. **Sistemas de alertas**: Genera notificaciones de desequilibrios
