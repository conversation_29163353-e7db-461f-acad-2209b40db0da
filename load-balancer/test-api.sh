#!/bin/bash

# 🧪 Script para probar la API del Load Balancer

PORT=${PORT:-3002}
BASE_URL="http://localhost:$PORT/api"

echo "🧪 Testing Load Balancer API at $BASE_URL"
echo "========================================"

# Función para hacer requests
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    
    echo ""
    echo "📋 Testing: $description"
    echo "🔗 $method $BASE_URL$endpoint"
    echo "---"
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint" | jq . 2>/dev/null || curl -s "$BASE_URL$endpoint"
    elif [ "$method" = "POST" ]; then
        curl -s -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
             -X POST \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint" | jq . 2>/dev/null || curl -s -X POST "$BASE_URL$endpoint"
    fi
}

# Verificar si el servidor está corriendo
echo "🔍 Checking if server is running..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ Server is not running at $BASE_URL"
    echo "💡 Start the load balancer first: bun run start"
    exit 1
fi

echo "✅ Server is running!"

# Probar endpoints
test_endpoint "GET" "/" "API Documentation"
test_endpoint "GET" "/health" "Health Check"
test_endpoint "GET" "/load-balancer/info" "Load Balancer Information"
test_endpoint "GET" "/load-balancer/stats" "Load Balancer Statistics"
test_endpoint "GET" "/load-balancer/status" "Load Balancer Status (Combined)"
test_endpoint "GET" "/workers" "Registered Workers"
test_endpoint "GET" "/metrics" "Prometheus Metrics"

echo ""
echo "🎉 API Testing Complete!"
echo ""
echo "💡 Useful commands:"
echo "   Health check: curl $BASE_URL/health"
echo "   Load balancer stats: curl $BASE_URL/load-balancer/stats | jq"
echo "   Workers list: curl $BASE_URL/workers | jq"
echo "   All endpoints: curl $BASE_URL/ | jq"
