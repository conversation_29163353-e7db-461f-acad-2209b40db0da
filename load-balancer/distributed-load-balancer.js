#!/usr/bin/env node

/**
 * Load Balancer Distribuido para múltiples instancias
 */

import { MongoClient } from 'mongodb';
import { createLogger, docmqConfig } from './docmq-config.js';

class DistributedLoadBalancer {
    constructor(options = {}) {
        this.instanceId = options.instanceId || `lb-${process.pid}`;
        this.mongoUri = options.mongoUri || docmqConfig.driver.options.mongo.uri;
        this.database = options.database || docmqConfig.driver.options.mongo.database;

        this.logger = createLogger(`LoadBalancer-${this.instanceId}`);

        this.client = null;
        this.db = null;
        this.instanceStatsCollection = null;
        this.loadBalancerCollection = null;

        // Configuración del load balancer
        this.config = {
            healthCheckInterval: options.healthCheckInterval || 30000, // 30s
            rebalanceInterval: options.rebalanceInterval || 120000,    // 2min
            instanceTimeout: options.instanceTimeout || 90000,        // 90s
            maxJobsPerInstance: options.maxJobsPerInstance || 1000,
            targetUtilization: options.targetUtilization || 0.8       // 80%
        };

        this.instances = new Map();
        this.maxInstances = options.maxInstances || 50; // Límite de seguridad
        this.isRunning = false;
    }

    async initialize() {
        try {
            this.logger.info('🚀 Inicializando Load Balancer Distribuido');

            // Conectar a MongoDB
            this.client = new MongoClient(this.mongoUri);
            await this.client.connect();
            this.db = this.client.db(this.database);

            // Crear colecciones
            this.instanceStatsCollection = this.db.collection('instance_stats');
            this.loadBalancerCollection = this.db.collection('load_balancer_control');

            // Crear índices
            await this.createIndexes();

            this.logger.success('✅ Load Balancer inicializado');
            return true;
        } catch (error) {
            this.logger.error(`❌ Error inicializando Load Balancer: ${error.message}`);
            throw error;
        }
    }

    async createIndexes() {
        // Índice para instance stats con TTL
        await this.instanceStatsCollection.createIndex(
            { lastHeartbeat: 1 },
            { expireAfterSeconds: 300 } // 5 minutos TTL
        );

        await this.instanceStatsCollection.createIndex({ instanceId: 1 }, { unique: true });
        await this.loadBalancerCollection.createIndex({ type: 1 });
    }

    async start() {
        try {
            await this.initialize();
            this.isRunning = true;

            this.showLoadBalancerInfo();

            // Iniciar monitoreo
            this.startHealthChecks();
            this.startRebalancing();

            // Manejo de señales
            process.on('SIGINT', () => this.gracefulShutdown());
            process.on('SIGTERM', () => this.gracefulShutdown());

            this.logger.success('✅ Load Balancer iniciado');

        } catch (error) {
            this.logger.error('❌ Error iniciando Load Balancer:', error.message);
            process.exit(1);
        }
    }

    startHealthChecks() {
        setInterval(async () => {
            if (!this.isRunning) return;

            try {
                await this.performHealthCheck();
            } catch (error) {
                this.logger.error(`❌ Error en health check: ${error.message}`);
            }
        }, this.config.healthCheckInterval);
    }

    startRebalancing() {
        setInterval(async () => {
            if (!this.isRunning) return;

            try {
                await this.performRebalancing();
            } catch (error) {
                this.logger.error(`❌ Error en rebalancing: ${error.message}`);
            }
        }, this.config.rebalanceInterval);
    }



    async performHealthCheck() {
        this.logger.info('🔍 Realizando health check de instancias...');

        // Obtener instancias activas
        const cutoffTime = new Date(Date.now() - this.config.instanceTimeout);
        const activeInstances = await this.instanceStatsCollection.find({
            lastHeartbeat: { $gte: cutoffTime }
        }).toArray();

        // Actualizar mapa de instancias con límite de seguridad
        this.instances.clear();

        // Limitar número de instancias para evitar consumo excesivo de memoria
        const limitedInstances = activeInstances.slice(0, this.maxInstances);

        if (activeInstances.length > this.maxInstances) {
            this.logger.warn(`⚠️ Limitando instancias: ${activeInstances.length} → ${this.maxInstances}`);
        }

        for (const instance of limitedInstances) {
            this.instances.set(instance.instanceId, {
                ...instance,
                status: 'healthy'
            });
        }

        this.logger.info(`📊 Instancias activas: ${this.instances.size}`);

        // Detectar instancias inactivas
        const inactiveInstances = await this.instanceStatsCollection.find({
            lastHeartbeat: { $lt: cutoffTime }
        }).toArray();

        if (inactiveInstances.length > 0) {
            this.logger.warn(`⚠️  Instancias inactivas detectadas: ${inactiveInstances.length}`);
            for (const instance of inactiveInstances) {
                this.logger.warn(`   💀 ${instance.instanceId} - Última actividad: ${instance.lastHeartbeat}`);
            }
        }
    }

    async performRebalancing() {
        if (this.instances.size === 0) {
            this.logger.warn('⚠️  No hay instancias activas para rebalancear');
            return;
        }

        this.logger.info('⚖️  Realizando rebalancing de carga...');

        // Calcular métricas de carga
        const loadMetrics = await this.calculateLoadMetrics();

        // Determinar si necesita rebalancing
        const needsRebalancing = this.needsRebalancing(loadMetrics);

        if (needsRebalancing) {
            await this.executeRebalancing(loadMetrics);
        } else {
            this.logger.info('✅ Carga balanceada correctamente');
        }
    }

    async calculateLoadMetrics() {
        const metrics = {
            totalInstances: this.instances.size,
            totalConcurrency: 0,
            totalActiveJobs: 0,
            totalProcessedJobs: 0,
            avgUtilization: 0,
            instances: []
        };

        // Iterar directamente sobre el Map sin crear array temporal
        for (const [instanceId, instance] of this.instances) {
            const utilization = instance.concurrency > 0
                ? (instance.activeJobs || 0) / instance.concurrency
                : 0;

            metrics.totalConcurrency += instance.concurrency || 0;
            metrics.totalActiveJobs += instance.activeJobs || 0;
            metrics.totalProcessedJobs += instance.processedJobs || 0;

            // Solo agregar al array si es necesario para el resultado
            metrics.instances.push({
                instanceId: instance.instanceId,
                concurrency: instance.concurrency || 0,
                activeJobs: instance.activeJobs || 0,
                processedJobs: instance.processedJobs || 0,
                utilization,
                jobsPerMinute: instance.jobsPerMinute || 0,
                lastHeartbeat: instance.lastHeartbeat
            });
        }

        metrics.avgUtilization = metrics.totalConcurrency > 0
            ? metrics.totalActiveJobs / metrics.totalConcurrency
            : 0;

        return metrics;
    }

    needsRebalancing(metrics) {
        // Verificar si alguna instancia está sobrecargada
        const overloadedInstances = metrics.instances.filter(
            instance => instance.utilization > this.config.targetUtilization
        );

        // Verificar si hay desequilibrio significativo
        const utilizationValues = metrics.instances.map(i => i.utilization);
        const maxUtilization = Math.max(...utilizationValues);
        const minUtilization = Math.min(...utilizationValues);
        const utilizationGap = maxUtilization - minUtilization;

        return overloadedInstances.length > 0 || utilizationGap > 0.3;
    }

    async executeRebalancing(metrics) {
        this.logger.info('🔄 Ejecutando rebalancing...');

        // Ordenar instancias por utilización
        const sortedInstances = metrics.instances.sort((a, b) => a.utilization - b.utilization);

        const underutilized = sortedInstances.filter(i => i.utilization < 0.5);
        const overutilized = sortedInstances.filter(i => i.utilization > 0.8);

        if (underutilized.length > 0 && overutilized.length > 0) {
            // Crear recomendaciones de rebalancing
            const recommendations = {
                timestamp: new Date(),
                type: 'rebalancing_recommendation',
                underutilized: underutilized.map(i => ({
                    instanceId: i.instanceId,
                    utilization: i.utilization,
                    availableCapacity: i.concurrency - i.activeJobs
                })),
                overutilized: overutilized.map(i => ({
                    instanceId: i.instanceId,
                    utilization: i.utilization,
                    excessLoad: i.activeJobs - (i.concurrency * 0.8)
                })),
                recommendation: 'Consider redistributing load or scaling instances'
            };

            await this.loadBalancerCollection.replaceOne(
                { type: 'rebalancing_recommendation' },
                recommendations,
                { upsert: true }
            );

            this.logger.info(`📊 Recomendación creada: ${underutilized.length} subutilizadas, ${overutilized.length} sobreutilizadas`);
        }
    }



    showLoadBalancerInfo() {
        console.log('='.repeat(80));
        console.log('🌐 LOAD BALANCER DISTRIBUIDO');
        console.log('='.repeat(80));
        console.log(`📋 Load Balancer ID: ${this.instanceId}`);
        console.log(`🗄️  Base de datos: ${this.database}`);
        console.log(`⏱️  Health check: cada ${this.config.healthCheckInterval / 1000}s`);
        console.log(`⚖️  Rebalancing: cada ${this.config.rebalanceInterval / 1000}s`);
        console.log(`🎯 Target utilization: ${(this.config.targetUtilization * 100).toFixed(0)}%`);
        console.log(`⏱️  Iniciado: ${new Date().toLocaleString()}`);
        console.log('='.repeat(80));
        console.log('🎯 Monitoreando instancias distribuidas...\n');
    }

    async showCurrentStatus() {
        if (this.instances.size === 0) {
            console.log('⚠️  No hay instancias activas');
            return;
        }

        const metrics = await this.calculateLoadMetrics();

        console.log('-'.repeat(80));
        console.log('📊 ESTADO ACTUAL DEL LOAD BALANCER');
        console.log('-'.repeat(80));
        console.log(`🌐 Instancias activas: ${metrics.totalInstances}`);
        console.log(`🔄 Concurrencia total: ${metrics.totalConcurrency}`);
        console.log(`📊 Jobs activos: ${metrics.totalActiveJobs}`);
        console.log(`📈 Jobs procesados: ${metrics.totalProcessedJobs}`);
        console.log(`⚖️  Utilización promedio: ${(metrics.avgUtilization * 100).toFixed(1)}%`);

        console.log('\n📋 INSTANCIAS:');
        metrics.instances.forEach((instance, index) => {
            const status = instance.utilization > 0.8 ? '🔴' :
                          instance.utilization > 0.5 ? '🟡' : '🟢';

            console.log(`   ${index + 1}. ${status} ${instance.instanceId}`);
            console.log(`      Concurrencia: ${instance.concurrency}, Activos: ${instance.activeJobs}`);
            console.log(`      Utilización: ${(instance.utilization * 100).toFixed(1)}%, Jobs/min: ${instance.jobsPerMinute}`);
        });

        console.log('-'.repeat(80) + '\n');
    }

    async gracefulShutdown() {
        this.logger.info('🛑 Cerrando Load Balancer...');
        this.isRunning = false;

        try {
            if (this.client) {
                await this.client.close();
            }
            this.logger.success('✅ Load Balancer cerrado correctamente');
            process.exit(0);
        } catch (error) {
            this.logger.error('❌ Error cerrando Load Balancer:', error.message);
            process.exit(1);
        }
    }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    const args = process.argv.slice(2);
    const options = {};

    for (let i = 0; i < args.length; i += 2) {
        const key = args[i]?.replace('--', '');
        const value = args[i + 1];

        switch (key) {
            case 'instance':
                options.instanceId = `lb-${value}`;
                break;
            case 'health-interval':
                options.healthCheckInterval = parseInt(value) * 1000;
                break;
            case 'rebalance-interval':
                options.rebalanceInterval = parseInt(value) * 1000;
                break;
        }
    }

    const loadBalancer = new DistributedLoadBalancer(options);

    // Mostrar estado cada 30 segundos
    setInterval(async () => {
        if (loadBalancer.isRunning) {
            await loadBalancer.showCurrentStatus();
        }
    }, 30000);

    loadBalancer.start();
}

export { DistributedLoadBalancer };
