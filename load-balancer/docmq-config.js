import os from 'os';
import dotenv from "dotenv"


dotenv.config()
export const docmqConfig = {
    // Configuración del driver MongoDB
    driver: {
        type: 'mongo',
        options: {
            mongo: {
                uri: process.env.MONGODB_URI,
                database: process.env.MONGODB_DB_NAME,
                collection: process.env.COLLECTION_QUEUES_NAME, // Colección SEPARADA para jobs de DocMQ
                options: {
                    useNewUrlParser: true,
                    useUnifiedTopology: true
                }
            }
        }
    },

    // Configuración de colas
    queues: {
        file_processing_tasks: {
            name: process.env.COLLECTION_QUEUES_NAME, // Debe coincidir con la colección del driver
            options: {
                retention: {
                    jobs: 3600 // 1 hora
                },
                statInterval: 10 // estadísticas cada 10 segundos
            },
            processor: {
                concurrency: process.env.WORKER_COUNT, // Forzar 1 job a la vez
                visibility: 600, // 1 segundo de visibilidad
                pollInterval: 200, // polling cada 200ms
                pause: false
            }
        }
    },

    // Configuración de workers
    workers: {
        defaultConcurrency: 1,
        maxConcurrency: 5,
        processingTimeout: 30000, // 30 segundos
        retryAttempts: 3,
        retryDelay: 5000,
        gracefulShutdownTimeout: 10000, // 10 segundos para shutdown
        sleepTimeMs: {
            min: 1000, // 1 segundo mínimo
            max: 5000  // 5 segundos máximo
        }
    },

    // Configuración del cluster
    cluster: {
        defaultWorkers: Math.max(2, Math.floor(os.cpus().length / 2)),
        maxWorkers: os.cpus().length,
        autoScale: {
            enabled: false,
            thresholds: {
                scaleUp: 10, // escalar si hay más de 10 trabajos pendientes
                scaleDown: 2  // reducir si hay menos de 2 trabajos pendientes
            }
        }
    },

    // Configuración de monitoreo
    monitoring: {
        enabled: true,
        statsInterval: 30000, // estadísticas cada 30 segundos
        logLevel: process.env.LOG_LEVEL || 'info'
    }
};

/**
 * Factory para crear el driver MongoDB
 */
export async function createDriver() {
    const {driver} = docmqConfig;

    try {
        // Importación dinámica para MongoDB
        const {MongoDriver} = await import('docmq/driver/mongo');
        return new MongoDriver(
            driver.options.mongo.uri,
            {
                schema: driver.options.mongo.database,  // DocMQ usa 'schema' para database
                table: driver.options.mongo.collection, // DocMQ usa 'table' para collection
                strict: true
            }
        );
    } catch (error) {
        console.error('Error creando driver MongoDB:', error.message);
        console.error('Asegúrate de que MongoDB esté disponible y la URI sea correcta');
        throw error;
    }
}

/**
 * Configuración específica para la cola de procesamiento de archivos
 */
export function getFileProcessingQueueConfig() {
    return docmqConfig.queues.file_processing_tasks;
}

/**
 * Configuración para workers
 */
export function getWorkerConfig() {
    return docmqConfig.workers;
}

/**
 * Configuración para el cluster
 */
export function getClusterConfig() {
    return docmqConfig.cluster;
}

/**
 * Utilidad para logging consistente
 */
export function createLogger(component) {
    const logLevel = docmqConfig.monitoring.logLevel;

    return {
        info: (message, ...args) => {
            if (['info', 'debug'].includes(logLevel)) {
                console.log(`[${component}] ℹ️  ${message}`, ...args);
            }
        },
        warn: (message, ...args) => {
            if (['info', 'warn', 'debug'].includes(logLevel)) {
                console.warn(`[${component}] ⚠️  ${message}`, ...args);
            }
        },
        error: (message, ...args) => {
            console.error(`[${component}] ❌ ${message}`, ...args);
        },
        debug: (message, ...args) => {
            if (logLevel === 'debug') {
                console.log(`[${component}] 🐛 ${message}`, ...args);
            }
        },
        success: (message, ...args) => {
            if (['info', 'debug'].includes(logLevel)) {
                console.log(`[${component}] ✅ ${message}`, ...args);
            }
        }
    };
}
