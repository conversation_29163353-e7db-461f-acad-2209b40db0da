#!/usr/bin/env node

/**
 * Sistema de deduplicación distribuido usando MongoDB
 */

import {MongoClient} from 'mongodb';
import {createLogger, docmqConfig} from './docmq-config.js';
import {mongoConnect} from "./mongoConnect.js";

class MongoDeduplication {
    constructor(options = {}) {
        this.mongoUri = options.mongoUri || docmqConfig.driver.options.mongo.uri;
        this.database = options.database || docmqConfig.driver.options.mongo.database;
        this.collection = options.collection || 'processed_jobs';
        this.ttl = options.ttl || 3600; // 1 hora
        this.workerId = options.workerId || 'unknown';
        this.logger = createLogger(`MongoDedup-${this.workerId}`);

        this.client = null;
        this.db = null;
        this.processedCollection = null;
    }

    /**
     * Inicializar conexión y crear índices
     */
    async initialize() {
        try {
            const {client, db} = await mongoConnect()
            this.client = client
            this.db = db
            this.processedCollection = this.db.collection(this.collection);

            // Crear índices
            await this.processedCollection.createIndex(
                {jobId: 1},
                {unique: true}
            );

            // Índice TTL para auto-eliminación
            await this.processedCollection.createIndex(
                {processedAt: 1},
                {expireAfterSeconds: this.ttl}
            );

            this.logger.info(`🗄️  MongoDB deduplication initialized: TTL=${this.ttl}s`);
            return true;
        } catch (error) {
            this.logger.error(`❌ Error initializing MongoDB dedup: ${error.message}`);
            throw error;
        }
    }

    /**
     * Verificar si un job ya fue procesado
     */
    async isProcessed(jobId) {
        try {
            const result = await this.processedCollection.findOne({jobId});

            if (result) {
                this.logger.info(`⚠️  Job ${jobId} ya procesado por ${result.workerId} en ${result.processedAt}`);
                return {processed: true, data: result};
            }

            return {processed: false};
        } catch (error) {
            this.logger.error(`❌ Error checking MongoDB: ${error.message}`);
            // Fail-safe: permitir procesamiento en caso de error
            return {processed: false, error: error.message};
        }
    }

    /**
     * Marcar job como procesado (con upsert atómico)
     */
    async markAsProcessed(jobId, metadata = {}) {
        try {
            const doc = {
                jobId,
                workerId: this.workerId,
                processedAt: new Date(),
                ...metadata
            };

            // Usar upsert para evitar duplicados
            const result = await this.processedCollection.replaceOne(
                {jobId},
                doc,
                {upsert: true}
            );

            if (result.upsertedCount > 0) {
                this.logger.info(`📝 Job ${jobId} marcado como procesado`);
                return {success: true, inserted: true};
            } else if (result.modifiedCount > 0) {
                this.logger.info(`📝 Job ${jobId} actualizado como procesado`);
                return {success: true, updated: true};
            } else {
                this.logger.warn(`⚠️  Job ${jobId} ya existía sin cambios`);
                return {success: true, noChange: true};
            }
        } catch (error) {
            if (error.code === 11000) { // Duplicate key error
                this.logger.warn(`⚠️  Job ${jobId} ya fue marcado por otro worker`);
                return {success: false, duplicate: true};
            }

            this.logger.error(`❌ Error marking in MongoDB: ${error.message}`);
            return {success: false, error: error.message};
        }
    }

    /**
     * Intentar adquirir lock atómico para procesamiento
     */
    async acquireProcessingLock(jobId, lockTtl = 300) {
        try {
            const lockDoc = {
                jobId: `lock:${jobId}`,
                workerId: this.workerId,
                processedAt: new Date(),
                lockExpires: new Date(Date.now() + lockTtl * 1000),
                type: 'processing_lock'
            };

            // Intentar insertar lock
            const result = await this.processedCollection.insertOne(lockDoc);

            if (result.insertedId) {
                this.logger.info(`🔒 Lock adquirido para job ${jobId}`);
                return {acquired: true, lockId: result.insertedId};
            }

            return {acquired: false};
        } catch (error) {
            if (error.code === 11000) { // Duplicate key - lock ya existe
                this.logger.warn(`⚠️  Lock ya existe para job ${jobId}`);
                return {acquired: false, duplicate: true};
            }

            this.logger.error(`❌ Error acquiring lock: ${error.message}`);
            return {acquired: false, error: error.message};
        }
    }

    /**
     * Liberar lock de procesamiento
     */
    async releaseProcessingLock(jobId) {
        try {
            const result = await this.processedCollection.deleteOne({
                jobId: `lock:${jobId}`,
                workerId: this.workerId
            });

            if (result.deletedCount > 0) {
                this.logger.info(`🔓 Lock liberado para job ${jobId}`);
                return true;
            } else {
                this.logger.warn(`⚠️  Lock no encontrado para liberar: ${jobId}`);
                return false;
            }
        } catch (error) {
            this.logger.error(`❌ Error releasing lock: ${error.message}`);
            return false;
        }
    }

    /**
     * Obtener estadísticas
     */
    async getStats() {
        try {
            const processedJobs = await this.processedCollection.countDocuments({
                type: {$ne: 'processing_lock'}
            });

            const activeLocks = await this.processedCollection.countDocuments({
                type: 'processing_lock'
            });

            const byWorker = await this.processedCollection.aggregate([
                {$match: {type: {$ne: 'processing_lock'}}},
                {$group: {_id: '$workerId', count: {$sum: 1}}}
            ]).toArray();

            return {
                processedJobs,
                activeLocks,
                byWorker
            };
        } catch (error) {
            this.logger.error(`❌ Error getting stats: ${error.message}`);
            return {error: error.message};
        }
    }

    /**
     * Limpiar locks expirados
     */
    async cleanupExpiredLocks() {
        try {
            const result = await this.processedCollection.deleteMany({
                type: 'processing_lock',
                lockExpires: {$lt: new Date()}
            });

            if (result.deletedCount > 0) {
                this.logger.info(`🧹 ${result.deletedCount} locks expirados eliminados`);
            }

            return result.deletedCount;
        } catch (error) {
            this.logger.error(`❌ Error cleaning expired locks: ${error.message}`);
            return 0;
        }
    }

    /**
     * Cerrar conexión
     */
    async close() {
        try {
            if (this.client) {
                await this.client.close();
                this.logger.info('🔌 Conexión MongoDB cerrada');
            }
        } catch (error) {
            this.logger.error(`❌ Error closing MongoDB: ${error.message}`);
        }
    }
}

export {MongoDeduplication};
