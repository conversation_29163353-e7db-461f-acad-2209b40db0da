import {MongoClient} from "mongodb";
import {docmqConfig} from "./docmq-config.js";

let cachedClient = null;
let cachedDb = null;
let connectionPromise = null;
const uri = docmqConfig.driver.options.mongo.uri;

export const mongoConnect = async (databaseName = 'conversion_finder') => {

    if (cachedClient && cachedDb) {
        try {
            await cachedClient.db('admin').command({ping: 1});
            return {client: cachedClient, db: cachedDb};
        } catch (error) {
            console.warn('Cached MongoDB connection is stale, reconnecting...');
            try {
                await cachedClient.close();
            } catch (closeError) {
                console.debug('Failed to close stale client:', closeError.message);
            }
            cachedClient = null;
            cachedDb = null;
        }
    }

    if (connectionPromise) {
        return await connectionPromise;
    }

    connectionPromise = (async () => {
        const client = new MongoClient(uri, {
            maxPoolSize: 50,
            minPoolSize: 5,
            maxIdleTimeMS: 30000,
            serverSelectionTimeoutMS: 10000,
            socketTimeoutMS: 300000,
            connectTimeoutMS: 20000,
            heartbeatFrequencyMS: 10000,
            retryWrites: true,
            retryReads: true
        });

        try {
            await client.connect();
            const db = client.db(databaseName);

            await db.command({ping: 1});

            cachedClient = client;
            cachedDb = db;
            connectionPromise = null;

            console.log('✅ MongoDB connected successfully');
            return {client, db};
        } catch (error) {
            connectionPromise = null;
            console.error('❌ MongoDB connection failed:', error.message);
            throw error;
        }
    })();

    return await connectionPromise;
};
