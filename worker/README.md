# 👷 Worker Microservice with API

Microservicio worker con load balancing automático, API REST integrada y monitoreo en tiempo real que se registra con el load balancer distribuido y procesa jobs de DocMQ.

## 🚀 Características

- ✅ **Auto-registro**: Se registra automáticamente con el load balancer
- ✅ **API REST integrada**: Endpoints para monitoreo y control usando Hono
- ✅ **Deduplicación distribuida**: Previene procesamiento duplicado entre instancias
- ✅ **Monitoreo en tiempo real**: Reporta estadísticas al load balancer
- ✅ **Health checks**: Endpoints para verificar estado del worker
- ✅ **Métricas Prometheus**: Formato compatible con Grafana/Prometheus
- ✅ **Fault tolerance**: Manejo de errores y reconexión automática
- ✅ **Escalabilidad**: Fácil agregar múltiples instancias

## 📦 Instalación

```bash
cd worker
bun install
```

## 🌐 API REST Integrada

El worker incluye una API REST completa para monitoreo y control:

### **🔗 Endpoints Disponibles:**

| Endpoint | Método | Descripción |
|----------|--------|-------------|
| `/api/` | GET | Lista de todos los endpoints |
| `/api/health` | GET | Health check básico |
| `/api/worker/info` | GET | Información del worker |
| `/api/worker/stats` | GET | Estadísticas detalladas |
| `/api/worker/status` | GET | Estado combinado |
| `/api/worker/pause` | POST | Pausar worker (futuro) |
| `/api/worker/resume` | POST | Reanudar worker (futuro) |
| `/api/metrics` | GET | Métricas formato Prometheus |

### **📊 Ejemplos de Uso:**

```bash
# Health check
curl http://localhost:3000/api/health

# Estadísticas del worker
curl http://localhost:3000/api/worker/stats | jq

# Estado completo
curl http://localhost:3000/api/worker/status | jq

# Métricas para Prometheus
curl http://localhost:3000/api/metrics
```

### **🎯 Respuestas de Ejemplo:**

#### **Health Check:**
```json
{
  "status": "ok",
  "service": "stream-docmq-worker",
  "version": "1.0.0",
  "timestamp": "2025-05-29T00:45:00.000Z",
  "uptime": 1234.5,
  "environment": "development"
}
```

#### **Worker Stats:**
```json
{
  "processedTasks": 156,
  "skippedTasks": 3,
  "activeTasks": 8,
  "errors": 0,
  "uptime": 1800,
  "jobsPerMinute": 31,
  "utilization": 50.0,
  "lastJobAt": 1732838700000,
  "startTime": 1732836900000,
  "concurrency": 16
}
```

## ⚙️ Configuración

El worker usa la configuración compartida en `../shared/docmq-config.js`. Asegúrate de configurar:

- **MongoDB URI**: Para conectar a la base de datos
- **Concurrencia**: Número de jobs simultáneos por worker

## 🚀 Uso

### **Desarrollo:**
```bash
bun run dev
# Inicia worker con instance ID 'dev'
```

### **Producción:**
```bash
bun start
# O con instance ID específico:
bun run prod
```

### **Testing de API:**
```bash
# Probar todos los endpoints
bun run test-api

# Health check rápido
bun run api-health

# Ver estadísticas
bun run api-stats
```

### **Personalizado:**
```bash
node load-balanced-worker.js --instance=server-1 --concurrency=16 --server=ServerA
```

## 📊 Parámetros

- `--instance=<id>`: ID único del worker (default: auto-generado)
- `--concurrency=<num>`: Número de jobs simultáneos (default: CPUs * 2)
- `--server=<name>`: Nombre del servidor para identificación

## 🌐 Integración con Load Balancer

El worker automáticamente:

1. **Se registra** con el load balancer al iniciar
2. **Reporta estadísticas** cada 30 segundos
3. **Envía heartbeat** para health checks
4. **Se desregistra** al cerrar gracefully

## 📊 Estadísticas Reportadas

```javascript
{
  instanceId: 'worker-server-1',
  concurrency: 16,
  activeJobs: 8,
  processedJobs: 156,
  jobsPerMinute: 22,
  utilization: 50%, // 8/16
  serverInfo: {
    hostname: 'server-1',
    cpus: 8,
    memory: '16GB'
  }
}
```

## 🔧 Variables de Entorno

```bash
# MongoDB
MONGODB_URI=mongodb://localhost:27017/docmq
MONGODB_DB=queue_system

# Worker
WORKER_CONCURRENCY=16
WORKER_INSTANCE_ID=worker-1
SERVER_NAME=production-server-1
```

## 🐳 Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 📈 Escalabilidad

### **Horizontal (Múltiples Servidores):**
```bash
# Servidor 1
npm start

# Servidor 2
npm start

# Servidor 3
npm start
# Cada worker se registra automáticamente
```

### **Vertical (Más Concurrencia):**
```bash
node load-balanced-worker.js --concurrency=32
```

## 🚨 Troubleshooting

### **High CPU/Memory Usage Issues**

If workers are consuming 99% CPU/Memory, follow these steps:

#### **Immediate Actions:**
1. **Check Environment Variables:**
   ```bash
   # Reduce polling frequency
   export WORKER_POLL_INTERVAL=2000  # Default: 1000ms

   # Reduce concurrency
   export WORKER_COUNT=1  # Start with 1

   # Increase intervals
   export WORKER_STATS_INTERVAL=120000  # 2 minutes
   export WORKER_HEARTBEAT_INTERVAL=60000  # 1 minute
   ```

2. **Monitor Performance:**
   ```bash
   # Run performance monitor
   node performance-monitor.js

   # Check Docker stats
   docker stats queue-worker1
   ```

3. **Restart Workers:**
   ```bash
   docker-compose restart worker1 worker2 worker3
   ```

#### **Configuration Optimizations:**

**Memory Optimization:**
```bash
# Set Node.js memory limits
export NODE_OPTIONS="--max-old-space-size=512 --gc-interval=100 --expose-gc"

# Reduce cache sizes
export DEDUP_CACHE_MAX_SIZE=500
export MONGODB_MAX_POOL_SIZE=5
```

**CPU Optimization:**
```bash
# Increase polling intervals
export WORKER_POLL_INTERVAL=2000
export WORKER_STATS_INTERVAL=120000
export WORKER_CLEANUP_INTERVAL=1200000  # 20 minutes

# Reduce thread pool
export UV_THREADPOOL_SIZE=2
```

#### **Common Causes & Solutions:**

1. **Missing `await` in Promise Chains**
   - ✅ **Fixed**: Added proper `await` in queue processing
   - **Impact**: Prevents memory leaks from unhandled promises

2. **Aggressive Polling**
   - ✅ **Fixed**: Increased poll interval from 200ms to 1000ms
   - **Impact**: Reduces CPU usage by 80%

3. **Too Many Timers**
   - ✅ **Fixed**: Proper timer cleanup and reduced frequency
   - **Impact**: Prevents timer accumulation

4. **MongoDB Connection Leaks**
   - ✅ **Fixed**: Improved connection pooling and reuse
   - **Impact**: Prevents connection exhaustion

5. **Inefficient Database Queries**
   - ✅ **Fixed**: Added local caching and query optimization
   - **Impact**: Reduces database load by 60%

#### **Performance Monitoring:**

The worker now includes built-in performance monitoring:

```bash
# View real-time performance metrics
tail -f worker.log | grep "PERFORMANCE METRICS"

# Get performance report
curl http://localhost:3000/health  # If health endpoint available
```

**Warning Signs:**
- Memory usage > 400MB consistently
- CPU usage > 80% for extended periods
- GC frequency > 10 times per minute
- MongoDB connection pool exhaustion

#### **Emergency Recovery:**

If workers become unresponsive:

```bash
# 1. Stop all workers
docker-compose stop worker1 worker2 worker3

# 2. Clear any stuck jobs (optional)
mongo conversion_finder --eval "db.file_processing_queue.updateMany({}, {\$unset: {processing: 1}})"

# 3. Restart with reduced load
export WORKER_COUNT=1
export WORKER_POLL_INTERVAL=5000
docker-compose up worker1

# 4. Gradually scale up
docker-compose up worker2  # After confirming worker1 is stable
```

## 🚨 Original Troubleshooting

### **Worker no se registra:**
1. Verificar que el load balancer esté ejecutándose
2. Comprobar conexión a MongoDB
3. Revisar logs de conexión

### **Jobs no se procesan:**
1. Verificar que el producer esté creando jobs
2. Comprobar configuración de DocMQ
3. Revisar logs de deduplicación

### **Alta utilización:**
1. Aumentar concurrencia: `--concurrency=<num>`
2. Agregar más instancias de worker
3. Verificar rendimiento de MongoDB

## 📝 Logs

El worker muestra logs detallados:

```
🚀 WORKER LOAD-BALANCED
========================================
📋 Instance ID: lb-worker-server-1
🔄 Concurrencia: 16
🖥️  Servidor: server-1
🌐 Load Balancer: Registrado automáticamente

🔄 Procesando: job-123
⏱️  Simulando procesamiento por 2500ms...
✅ Job job-123 completado en 2502ms - Total: 156

📊 ESTADÍSTICAS DEL WORKER LOAD-BALANCED
----------------------------------------
📊 Jobs procesados: 156
⚠️  Jobs saltados: 3
⏱️  Uptime: 300s
⚡ Jobs/minuto: 31
🔄 Tareas activas: 8/16
📈 Utilización: 50.0%
🌐 Registrado con Load Balancer: ✅
```

## 🔗 Dependencias

- **DocMQ**: Sistema de colas
- **MongoDB**: Base de datos y deduplicación
- **Chalk**: Logs con colores
- **Shared**: Configuraciones compartidas
