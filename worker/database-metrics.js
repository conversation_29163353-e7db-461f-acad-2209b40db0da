#!/usr/bin/env node

/**
 * 📊 Database Metrics - Sistema de métricas basado en MongoDB
 * Todas las métricas se guardan en MongoDB, nada en memoria
 */

import { createLogger } from './docmq-config.js';

class DatabaseMetrics {
    constructor(options = {}) {
        this.instanceId = options.instanceId;
        this.db = options.db;
        this.logger = createLogger(`DBMetrics-${this.instanceId}`);

        this.metricsCollection = this.db.collection('worker_metrics');
        this.instanceStatsCollection = this.db.collection('instance_stats');

        this.startTime = Date.now();
        this.concurrency = options.concurrency || 1;

        this.logger.info('📊 Database Metrics inicializado');
    }

    async initialize() {
        try {
            await this.metricsCollection.createIndex(
                { instanceId: 1, timestamp: -1 },
                { background: true }
            );

            await this.metricsCollection.createIndex(
                { expiresAt: 1 },
                { expireAfterSeconds: 0, background: true }
            );

            await this.instanceStatsCollection.createIndex(
                { instanceId: 1 },
                { unique: true, background: true }
            );

            // Inicializar métricas del worker
            await this.initializeWorkerMetrics();

            this.logger.success('✅ Database Metrics inicializado con índices');
        } catch (error) {
            this.logger.error('❌ Error inicializando Database Metrics:', error.message);
            throw error;
        }
    }

    async initializeWorkerMetrics() {
        const initialMetrics = {
            instanceId: this.instanceId,
            processedTasks: 0,
            skippedTasks: 0,
            errors: 0,
            activeTasks: 0,
            totalProcessingTime: 0,
            startTime: new Date(this.startTime),
            lastJobAt: null,
            concurrency: this.concurrency,
            status: 'starting',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        await this.instanceStatsCollection.replaceOne(
            { instanceId: this.instanceId },
            initialMetrics,
            { upsert: true }
        );
    }

    async recordJobStart() {
        await this.instanceStatsCollection.updateOne(
            { instanceId: this.instanceId },
            {
                $inc: { activeTasks: 1 },
                $set: { updatedAt: new Date() }
            }
        );
    }

    async recordJobComplete(processingTime) {
        await this.instanceStatsCollection.updateOne(
            { instanceId: this.instanceId },
            {
                $inc: {
                    processedTasks: 1,
                    activeTasks: -1,
                    totalProcessingTime: processingTime
                },
                $set: {
                    lastJobAt: new Date(),
                    updatedAt: new Date()
                }
            }
        );

        // Guardar métrica detallada con TTL de 24 horas
        await this.metricsCollection.insertOne({
            instanceId: this.instanceId,
            type: 'job_completed',
            processingTime,
            timestamp: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // TTL 24h
        });
    }

    async recordJobSkipped(reason) {
        await this.instanceStatsCollection.updateOne(
            { instanceId: this.instanceId },
            {
                $inc: {
                    skippedTasks: 1,
                    activeTasks: -1
                },
                $set: { updatedAt: new Date() }
            }
        );

        // Guardar métrica detallada con TTL de 24 horas
        await this.metricsCollection.insertOne({
            instanceId: this.instanceId,
            type: 'job_skipped',
            reason,
            timestamp: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // TTL 24h
        });
    }

    async recordJobError(error) {
        await this.instanceStatsCollection.updateOne(
            { instanceId: this.instanceId },
            {
                $inc: {
                    errors: 1,
                    activeTasks: -1
                },
                $set: { updatedAt: new Date() }
            }
        );

        // Guardar métrica detallada con TTL de 24 horas
        await this.metricsCollection.insertOne({
            instanceId: this.instanceId,
            type: 'job_error',
            error: error.message,
            timestamp: new Date(),
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // TTL 24h
        });
    }

    async getWorkerStats() {
        try {
            const stats = await this.instanceStatsCollection.findOne(
                { instanceId: this.instanceId }
            );

            if (!stats) {
                return {
                    processedTasks: 0,
                    skippedTasks: 0,
                    errors: 0,
                    activeTasks: 0,
                    uptime: 0,
                    jobsPerMinute: 0,
                    utilization: 0,
                    concurrency: this.concurrency,
                    avgProcessingTime: 0
                };
            }

            const uptime = Math.floor((Date.now() - new Date(stats.startTime).getTime()) / 1000);
            const uptimeMinutes = uptime / 60;
            const jobsPerMinute = uptimeMinutes > 0 ? Math.round(stats.processedTasks / uptimeMinutes) : 0;
            const utilization = this.concurrency > 0 ? (stats.activeTasks / this.concurrency * 100) : 0;
            const avgProcessingTime = stats.processedTasks > 0 ?
                Math.round(stats.totalProcessingTime / stats.processedTasks) : 0;

            return {
                processedTasks: stats.processedTasks || 0,
                skippedTasks: stats.skippedTasks || 0,
                errors: stats.errors || 0,
                activeTasks: stats.activeTasks || 0,
                uptime,
                jobsPerMinute,
                utilization: Math.round(utilization * 100) / 100,
                concurrency: this.concurrency,
                avgProcessingTime,
                lastJobAt: stats.lastJobAt,
                startTime: stats.startTime
            };
        } catch (error) {
            this.logger.error('❌ Error obteniendo stats:', error.message);
            throw error;
        }
    }

    async getDetailedMetrics(hours = 1) {
        try {
            const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);

            const metrics = await this.metricsCollection.find({
                instanceId: this.instanceId,
                timestamp: { $gte: cutoffTime }
            }).sort({ timestamp: -1 }).toArray();

            const summary = {
                totalJobs: 0,
                completedJobs: 0,
                skippedJobs: 0,
                errorJobs: 0,
                avgProcessingTime: 0,
                minProcessingTime: null,
                maxProcessingTime: null,
                timeRange: `${hours}h`
            };

            let totalProcessingTime = 0;
            let processingTimes = [];

            for (const metric of metrics) {
                summary.totalJobs++;

                switch (metric.type) {
                    case 'job_completed':
                        summary.completedJobs++;
                        totalProcessingTime += metric.processingTime;
                        processingTimes.push(metric.processingTime);
                        break;
                    case 'job_skipped':
                        summary.skippedJobs++;
                        break;
                    case 'job_error':
                        summary.errorJobs++;
                        break;
                }
            }

            if (processingTimes.length > 0) {
                summary.avgProcessingTime = Math.round(totalProcessingTime / processingTimes.length);
                summary.minProcessingTime = Math.min(...processingTimes);
                summary.maxProcessingTime = Math.max(...processingTimes);
            }

            return summary;
        } catch (error) {
            this.logger.error('❌ Error obteniendo métricas detalladas:', error.message);
            throw error;
        }
    }

    async updateStatus(status) {
        await this.instanceStatsCollection.updateOne(
            { instanceId: this.instanceId },
            {
                $set: {
                    status,
                    updatedAt: new Date()
                }
            }
        );
    }

    async registerWithLoadBalancer() {
        try {
            const stats = await this.getWorkerStats();

            const instanceInfo = {
                instanceId: this.instanceId,
                concurrency: this.concurrency,
                activeJobs: stats.activeTasks,
                processedJobs: stats.processedTasks,
                jobsPerMinute: stats.jobsPerMinute,
                lastHeartbeat: new Date(),
                status: 'active'
            };

            await this.instanceStatsCollection.updateOne(
                { instanceId: this.instanceId },
                { $set: instanceInfo },
                { upsert: true }
            );

        } catch (error) {
            this.logger.error(`❌ Error registrando con load balancer: ${error.message}`);
        }
    }

    async cleanup() {
        try {
            await this.instanceStatsCollection.deleteOne({ instanceId: this.instanceId });
            this.logger.info('📤 Métricas del worker eliminadas');
        } catch (error) {
            this.logger.error('❌ Error en cleanup:', error.message);
        }
    }
}

export { DatabaseMetrics };
