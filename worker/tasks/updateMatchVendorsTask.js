import {connect} from "../mongo/connect";
import _ from "lodash";
import {bulkWriteMatchTask} from "../utils/bulkWriteMatchTask.js";

export const updateMatchVendorsTask = async (queue) => {
    const {db} = await connect()
    const limit = 10000;
    const queryToFindTransfers = {
        $or: [
            {vendor_id: ""},
            {vendor_id: null},
            {vendor_id: {$exists: false}}
        ],
        pub_id: {$ne: ""}
    };
    const getTransfersToUpdate = await db.collection("transfers").countDocuments(queryToFindTransfers);
    if (getTransfersToUpdate > 0) {
        console.log("Transfers vendors to update: ", getTransfersToUpdate);
        const vendors = await db.collection("vendors").find().project({_id: 1, vendor: 1, pub_ids: 1}).toArray() ?? [];
        const totalProcess = Math.ceil(getTransfersToUpdate / limit)
        let totalTransfersModified = 0;
        let totalPostbacksModified = 0;
        for (let i = 0; i < totalProcess; i++) {
            const transfers = await db.collection("transfers")
                .find(queryToFindTransfers).skip(limit * i).limit(limit).toArray();
            const bulkTransferOps = [];
            const bulkPostbackOps = [];
            for (let transfer of transfers) {
                const findVendor = checkVendorsPubIds(vendors, transfer.pub_id);
                const findPostback = await db.collection("postbacks").findOne({"transfer_id": transfer._id});

                if (findVendor) {
                    const objTRUpdate = {
                        vendor_id: findVendor?._id,
                        vendor: findVendor?.vendor,
                    };
                    bulkTransferOps.push({
                        updateOne: {
                            filter: { _id: transfer._id },
                            update: { $set: objTRUpdate }
                        }
                    });
                    if (findPostback) {
                        const objPBUpdate = {
                            vendor_id: findVendor?._id,
                        };
                        bulkPostbackOps.push({
                            updateOne: {
                                filter: { _id: transfer._id },
                                update: { $set: objPBUpdate }
                            }
                        });
                    }
                }
            }
            const getTotal = await bulkWriteMatchTask(bulkTransferOps, bulkPostbackOps);
            totalTransfersModified = getTotal.totalTransfersModified;
            totalPostbacksModified = getTotal.totalPostbacksModified;
        }
        return {totalTransfersModified, totalPostbacksModified}
    } else {
        return {totalTransfersModified: 0, totalPostbacksModified: 0}
    }
}

const checkVendorsPubIds = (vendors, pubId) => {
    return _.find(vendors, infVen =>
        _.some(infVen.pub_ids, {id: pubId})
    );
};