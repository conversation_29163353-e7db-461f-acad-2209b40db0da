import {buildFiltersPerformance} from "../utils/reports/performance/buildFiltersPerformance.js";
import {buildLeadsAgingAggregation} from "../utils/reports/performance/aggregations/buildLeadsAgingAggregation.js";
import {aggregationPerformanceReport} from "../utils/aggregations/aggregationPerformanceReport.js";
import {parallel, tryit} from "radash";
import {
    buildTransfersAgingAggregation
} from "../utils/reports/performance/aggregations/buildTransfersAgingAggregation.js";
import {groupDataAgingPerformance} from "../utils/reports/performance/aging/groupDataAgingPerformance.js";
import {
    buildPostbacksAgingAggregation
} from "../utils/reports/performance/aggregations/buildPostbacksAgingAggregation.js";
import {buildPostbacksResponse} from "../utils/reports/performance/aging/buildPostbacksResponse.js";
import {saveReport} from "../utils/reports/performance/saveReport.js";

export const saveAgingPerfRepTask = async (queue) => {
    const {payload} = queue;
    const {optionsReport, vendor, rangeDate, context} = payload;
    const [errorLeads, leadsResults] = await tryit(async () => {
        return getLeadsInfo(optionsReport, vendor, rangeDate, context);
    })();

    if (!errorLeads) {
        const objectTotal = {
            bucket: 'Total',
            leads: leadsResults.length,
            transfers: 0,
            "transfers>120": 0,
            "transfers<120": 0,
            postbacks: 0
        }
        let finalResults = [objectTotal]
        const leadsChunks = chunkArray(leadsResults, true, 50000);
        const chunks = chunkArray(leadsChunks, false, 10);
        const [errorTransfers, transfersResults] = await tryit(async () => {
            return getTransfersInfo(chunks);
        })();

        if (!errorTransfers) {
            const groupTransfersResults = groupDataAgingPerformance(transfersResults, finalResults);
            const [errorPostbacks, postbacksResults] = await tryit(async () => {
                return getPostbacks(groupTransfersResults);
            })();
            if (!errorPostbacks) {
                finalResults = buildPostbacksResponse(postbacksResults, finalResults);
                await saveReport(finalResults, {}, vendor._id, rangeDate, "aging");
                return finalResults
            } else {
                console.log("errorPostbacks");
            }
        } else {
            console.log("errorTransfers");
            // console.log(errorTransfers);
        }
    } else {
        return {total: 0}
    }
}

async function getLeadsInfo(optionsReport, vendor, rangeDate, context) {
    const valueReport = optionsReport.find((itm) => itm.collection === "leads");
    const {collection, hint, filters, key3, key5} = valueReport;
    const mongoFilters = buildFiltersPerformance(filters, rangeDate, key3, context);
    const getAggregation = buildLeadsAgingAggregation(key5, vendor._id, mongoFilters);
    return await aggregationPerformanceReport(collection, getAggregation, hint);
}

async function getTransfersInfo(chunks) {
    const chunkResponses = [];
    for (const chunk of chunks) {
        const responses = await parallel(chunk.length, chunk, async (infLeadId) => {
            const getAggregation = buildTransfersAgingAggregation(infLeadId);
            return await aggregationPerformanceReport("transfers", getAggregation, {});
        });
        chunkResponses.push(...responses);
    }
    return chunkResponses;
}

async function getPostbacks(allTransfersResponse) {
    return parallel(allTransfersResponse.length, allTransfersResponse, async (item) => {
        const getAggregation = buildPostbacksAgingAggregation(item?.ids);
        const finalResponse = await aggregationPerformanceReport("postbacks", getAggregation, {});
        return {
            bucket: item?._id,
            total: finalResponse[0]?.total ?? 0
        };
    });
}

const chunkArray = (array, divide, size) => {
    const ids = (divide) ? array.map(item => item._id) : array;
    const chunks = [];
    for (let i = 0; i < ids.length; i += size) {
        chunks.push(ids.slice(i, i + size));
    }
    return chunks;
}