import {getMappingConfiguration} from "../functions/mappings/getMappingConfiguration.js";
import {mappingData} from "../functions/mappings/mappingData.js";
import {mergeAdditionalData} from "../functions/mergeAdditionalData.js";
import {campaignKeyExistsInFile} from "../functions/campaignKeyExistsInFile.js";
import {assignCampaignKey} from "../functions/assignCampaignKey.js";
import _ from "lodash";
import {startPreConfigurations} from "../functions/preconfigurations/startPreConfigurations.js";
import {upsertData} from "../functions/upsertData.js";
import {docmqConfig} from "../docmq-config.js";
import {findAllByFilter} from "../mongo/findAllByFilter.js";
import {Collections} from "../mongo/collections.enum.js";
import {getFileRowsFromFile} from "../functions/getFileRowsFromFile.js";
import {TaskErrorsEnum} from "../functions/utils/task-errors.enum.js";

export const transfersTask = async (job) => {
    const jobId = job._id;
    try {
        if (!job || !job.payload) {
            throw new Error("Invalid job: missing payload");
        }
        const {payload} = job;
        let totalUpsertedCount = 0;
        let totalModifiedCount = 0;
        const {originalFileName, path, fileName, mappingConfiguration, splitFile} = payload;
        if (!originalFileName || !path || !splitFile || !mappingConfiguration) {
            throw new Error(`Missing required payload fields: originalFileName=${!!originalFileName}, path=${!!path}, splitFile=${!!splitFile}, mappingConfiguration=${!!mappingConfiguration}`, {cause: TaskErrorsEnum.MAPPING_CONFIGURATION_ERROR});
        }
        const {db_config, mongo} = mappingConfiguration;
        if (!db_config || !mongo) {
            throw new Error("Invalid mappingConfiguration: missing db_config or mongo");
        }
        console.log({
            jobId,
            originalFileName,
            fileName,
            splitFile,
            path
        }, "Processing transfer task");

        const folderSplit = originalFileName.replaceAll('.csv', '');
        const fileUrl = `${docmqConfig.databroker.uri}/public/${path}/${folderSplit}/${splitFile}`;
        console.log({jobId, fileUrl}, "Fetching file from data broker");
        const fileRows = await getFileRowsFromFile(path, folderSplit, splitFile);
        if (!fileRows || fileRows.length === 0) {
            console.warn({jobId, splitFile}, "File has no rows to process");
            return {success: true, rowsProcessed: 0, message: "No rows to process"};
        }

        console.log({jobId, splitFile, rowCount: fileRows.length}, "CSV parsed successfully");

        // Get mapping configuration
        const {
            constMappingConfiguration,
            preConfigurationMethods,
            mappingColumnsConfiguration
        } = getMappingConfiguration(mappingConfiguration);

        // Map data
        console.log({jobId}, "Mapping data");
        let mappedData = _.map(fileRows, (item) => {
            return mappingData(mappingColumnsConfiguration, item);
        });

        // Merge additional data
        mappedData = mergeAdditionalData(mappedData, constMappingConfiguration);

        // Handle campaign key
        const {hasCampaignKey, campaignKey} = campaignKeyExistsInFile(fileRows);
        if (hasCampaignKey) {
            assignCampaignKey(mappedData, campaignKey);
            console.log({jobId, campaignKey}, "Campaign key assigned");
        }

        console.log({jobId}, "Starting pre-configurations");

        // Get vendors and apply pre-configurations
        const vendors = await findAllByFilter(Collections.Vendors, {})
        mappedData = await startPreConfigurations(preConfigurationMethods, mappedData, vendors);

        // Upsert data
        const collection = Collections.Transfers;
        const mongoConfiguration = mongo.configuration;

        console.log({
            jobId,
            collection,
            dataCount: mappedData.length,
            mongoConfiguration
        }, "Upserting data to database");

        const result = await upsertData(
            collection,
            mappedData,
            mongoConfiguration,
            splitFile,
            totalUpsertedCount,
            totalModifiedCount,
            mappingConfiguration
        );

        console.log({
            jobId,
            splitFile,
            result,
            rowsProcessed: fileRows.length
        }, "Transfer task completed successfully");

        return {
            success: true,
            rowsProcessed: fileRows.length,
            result
        };

    } catch (error) {
        console.error({
            jobId,
            error: error.message,
            stack: error.stack,
            payload: job.payload ? {
                splitFile: job.payload.splitFile,
                originalFileName: job.payload.originalFileName,
                path: job.payload.path
            } : 'missing'
        }, "Transfer task failed");
        throw error;
    }
}
