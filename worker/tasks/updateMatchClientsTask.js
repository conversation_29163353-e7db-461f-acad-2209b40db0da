import {connect} from "../mongo/connect";
import _ from "lodash";
import {bulkWriteMatchTask} from "../utils/bulkWriteMatchTask.js";

export const updateMatchClientsTask = async () => {
    const {db} = await connect()
    const limit = 10000;
    const queryToFindTransfers = {
        $or: [
            {client_id: ""},
            {client_id: null},
            {client_id: {$exists: false}}
        ],
        transferred_to: {$ne: ""}
    };
    const getTransfersToUpdate = await db.collection("transfers").countDocuments(queryToFindTransfers);
    let totalTransfersModified = 0;
    let totalPostbacksModified = 0;
    if (getTransfersToUpdate > 0) {
        console.log("Transfers clients to update: ", getTransfersToUpdate);
        const clients = await db.collection("clients").find().project({_id: 1, client: 1, transferred_to: 1}).toArray() ?? [];
        const totalProcess = Math.ceil(getTransfersToUpdate / limit)
        for (let i = 0; i < totalProcess; i++) {
            const transfers = await db.collection("transfers")
                .find(queryToFindTransfers).skip(limit * i).limit(limit).toArray();
            const bulkTransferOps = [];
            const bulkPostbackOps = [];
            for (let transfer of transfers) {
                const findClient = checkClientsTransferredTo(clients, transfer.transferred_to);
                const findPostback = await db.collection("postbacks").findOne({ "transfer_id": transfer._id });

                if (findClient) {
                    const objTRUpdate = {
                        client_id: findClient?._id,
                    };
                    bulkTransferOps.push({
                        updateOne: {
                            filter: { _id: transfer._id },
                            update: { $set: objTRUpdate }
                        }
                    });
                    if (findPostback) {
                        const objPBUpdate = {
                            client_id: findClient?._id,
                        };
                        bulkPostbackOps.push({
                            updateOne: {
                                filter: { _id: transfer._id },
                                update: { $set: objPBUpdate }
                            }
                        });
                    }
                }
            }
            const getTotal = await bulkWriteMatchTask(bulkTransferOps, bulkPostbackOps);
            totalTransfersModified = getTotal.totalTransfersModified;
            totalPostbacksModified = getTotal.totalPostbacksModified;
        }
        return {totalTransfersModified, totalPostbacksModified}
    } else {
        return {totalTransfersModified: 0, totalPostbacksModified: 0}
    }
}

const checkClientsTransferredTo = (clients, transferNumber) => {
    return _.find(clients, infCli =>
        _.some(infCli.transferred_to, {number: transferNumber})
    );
};