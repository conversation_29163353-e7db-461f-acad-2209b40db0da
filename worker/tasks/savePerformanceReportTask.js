import {parallel, tryit} from "radash";
import {buildFiltersPerformance} from "../utils/reports/performance/buildFiltersPerformance.js";
import {
    buildAggregationPerformanceReport
} from "../utils/reports/performance/aggregations/buildAggregationPerformanceReport.js";
import {aggregationPerformanceReport} from "../utils/aggregations/aggregationPerformanceReport.js";
import {processFinalData} from "../utils/reports/performance/processFinalData.js";
import {saveReport} from "../utils/reports/performance/saveReport.js";

export const savePerformanceReportTask = async (queue) => {
    const {payload} = queue;
    const {optionsReport, vendor, rangeDate, context} = payload;
    const [error, finalResponse] = await tryit(async () => {
        return parallel(optionsReport.length, optionsReport, async (optRep) => {
            const valueReport = optionsReport.find((itm) => itm.collection === optRep?.collection);
            const {collection, hint, additions, filters, key1, key2, key3, key4, key5, key6} = valueReport;
            const allKeys = {key1, key2, key3, key4, key5, key6};
            const mongoFilters = buildFiltersPerformance(filters, rangeDate, key3, context);
            const getAggregation = buildAggregationPerformanceReport(vendor._id, mongoFilters, allKeys, additions);
            const response = await aggregationPerformanceReport(collection, getAggregation, hint);
            return {collection, response, vendor: vendor._id};
        });
    })();
    if (!error) {
        const {report, counts, vendor} = processFinalData(optionsReport, finalResponse);
        await saveReport(report, counts, vendor, rangeDate, "general");
        return counts ?? {total: 0}
    } else {
        return {totalReportsSaved: 0}
    }
}