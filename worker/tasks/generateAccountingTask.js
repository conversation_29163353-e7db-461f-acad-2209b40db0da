import {DateTime} from "luxon";
import Decimal from "decimal.js";
import _ from "lodash";
import {transformDateFilters} from "../functions/transformDateFilters.js";
import {getDecimalAmountByEventByLabel} from "../functions/accounting/getDecimalAmountByEventByLabel.js";
import {generateId} from "../functions/generateId.js";
import {getLeadId} from "../functions/accounting/getLeadId.js";
import {mongoConnect} from "../mongoConnect.js";
import {docmqConfig} from "../docmq-config.js";

export const generateAccountingTask = async (queue) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const {payload} = queue;
    let {
        collection,
        accountingType,
        items, invoiceAmount,
        invoiceAmountType,
        invoiceLabel,
        filters,
        invoiceIsoWeekDateFilter,
        invoice_id,
        amounts
    } = payload;

    const invoiceId = invoice_id;
    filters = transformDateFilters(filters);
    const accountingTypeFilter = accountingType === 'REVENUE' ? {invoice_revenue_id: null} : {invoice_expense_id: null};
    filters = {...filters, ...accountingTypeFilter}
    const counted = await db.collection(collection).countDocuments(filters);
    if (counted !== items) {
        console.log('Items changed')
        items = counted;
    }
    const totalAmount = getTotalAmount(invoiceAmountType, items, amounts);
    console.log('Total amount: ', totalAmount)
    const amountByItem = getAmountByItem(invoiceAmountType, items, amounts);
    const lineItems = getLineItems(amounts, items, invoiceAmountType)
    console.log('Amount by item: ', amountByItem)
    const invoice = {
        _id: invoiceId,
        week: invoiceIsoWeekDateFilter?.week[0],
        year: invoiceIsoWeekDateFilter.year,
        month: invoiceIsoWeekDateFilter.month + 1,
        label: invoiceLabel,
        total_events: items,
        total_amount: totalAmount,
        amount_by_event: amountByItem,
        revenue: accountingType === 'REVENUE',
        invoice_type: accountingType,
        line_items: lineItems,
        created_at: DateTime.utc(),
        collection,
        invoiceAmountType
    }
    const update = accountingType === 'REVENUE' ? {invoice_revenue_id: invoiceId} : {invoice_expense_id: invoiceId};
    console.log(`updating ${collection} with invoice id: ${invoiceId}`)
    await db.collection(collection).updateMany(filters, {$set: update});
    console.log(`Inserting invoice`)
    await db.collection('invoices').insertOne(invoice)
    let pageSize = 5000
    let page = 0;
    let hasNextPage = true;
    console.log(`Generate accounting start`)
    filters = {...filters, ...update}
    while (hasNextPage) {
        console.log(`Page: ${page}`);
        console.log('filters: ');
        console.log(JSON.stringify(filters))
        const itemsFound = await db.collection(collection).find(filters).limit(pageSize).skip(pageSize * page).toArray();
        console.log(`Items found ${itemsFound.length}`)
        const accountingArray = [];
        for (let amount of amounts) {
            const amountByEvent = getDecimalAmountByEventByLabel(invoiceAmountType, items, amount.amount);
            for (let item of itemsFound) {
                const _id = generateId()
                const accounting = {
                    _id,
                    lead_id: getLeadId(item, collection),
                    source_table: collection,
                    source_id: item._id,
                    type: accountingType,
                    vendor_id: item.vendor_id,
                    client_id: item.client_id,
                    tags: item.tags,
                    amount: amountByEvent,
                    invoice_id: invoiceId,
                    created_at: DateTime.utc(),
                    week: invoiceIsoWeekDateFilter?.week[0],
                    year: invoiceIsoWeekDateFilter.year,
                    month: invoiceIsoWeekDateFilter.month + 1,
                    pubid: collection === 'transfers' ? item.pub_id : item.pubid,
                    subid: collection === 'transfers' ? item.sub_id : item.subid,
                    campaign_key: item.campaign_key,
                    labelAmount: amount?.amountLabel
                }
                accountingArray.push(accounting);
            }
        }

        if (accountingArray && accountingArray.length > 0) {
            await db.collection('accounting').insertMany(accountingArray);
        }
        if (itemsFound.length < pageSize) {
            hasNextPage = false;
        }
        console.log(`Accounting insert end for page: ${page}`);
        page++;
    }
    console.log(`generate accounting end`);
}

const getLineItems = (amounts, items, invoiceAmountType) => {
    return _.map(amounts, item => {
        const amountDecimal = new Decimal(item.amount);
        const amount = amountDecimal.toDP(2).mul(100)
        const amountByEvent = invoiceAmountType === 'perItem' ? amount : amountDecimal.div(items).toDP(2).mul(100)
        const totalAmount = invoiceAmountType === 'perItem' ? amount.mul(items) : amount;
        return {
            label: item.amountLabel,
            amount_by_event: amountByEvent.toNumber(),
            total_amount: totalAmount.toNumber()
        }
    })
}

const getTotalAmount = (invoiceAmountType, items, amounts) => {
    const summaryTotal = _.sumBy(amounts, item => Number(item.amount));
    const amountDecimal = new Decimal(summaryTotal);
    const eventsDecimal = new Decimal(items)
    if (invoiceAmountType === 'all') {
        return amountDecimal.toDP(2).mul(100).toNumber();
    } else {
        return amountDecimal.mul(eventsDecimal).toDP(2).mul(100).toNumber()
    }
}


const getAmountByItem = (invoiceAmountType, items, amounts) => {
    const summaryTotal = _.sumBy(amounts, item => Number(item.amount));
    const amountDecimal = new Decimal(summaryTotal);
    const eventsDecimal = new Decimal(items)
    if (invoiceAmountType === 'all') {
        return amountDecimal.div(eventsDecimal).toDP(2).mul(100).toNumber();
    } else {
        return amountDecimal.toDP(2).mul(100).toNumber();
    }
}
