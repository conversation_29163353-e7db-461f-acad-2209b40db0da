import {tryit} from "radash";
import axios from "axios";
import {parseCsvStream} from "../functions/stream/parseCsvStream.js";
import {processPostbacks} from "../functions/postbacks/processPostbacks.js";
import {docmqConfig} from "../docmq-config.js";

export const postbackTask = async (job) => {
    const {payload} = job;
    const {client, type, fileName, path, mappingConfiguration, userEmail, splitFile} = payload;
    if (!client || !type) {
        throw {error: 'Client or type not found'}
    }
    let totalUpsertedCount = 0;
    let totalModifiedCount = 0;
    if (splitFile) {
        const folderSplit = fileName.replaceAll('.csv', '');
        const [error, response] = await tryit(axios)({
            url: `${docmqConfig.databroker.uri}/public/${path}/${folderSplit}/${splitFile}`,
            method: 'GET',
            responseType: 'stream',
        });
        if (error) {
            console.error(`Error fetching file: ${splitFile}`, error);
            return;
        }
        const stream = response.data;
        const fileRows = await parseCsvStream(stream, 0, "£");
        ({totalUpsertedCount, totalModifiedCount} = await processPostbacks(
            mappingConfiguration,
            client,
            fileRows,
            fileName,
            type,
            totalUpsertedCount,
            totalModifiedCount
        ));
        console.log('fileRows#length: ', fileRows.length)

    }
    return {totalUpsertedCount, totalModifiedCount}
}
