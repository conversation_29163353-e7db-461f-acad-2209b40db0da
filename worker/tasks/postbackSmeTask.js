import {processSmePostbacks} from "../functions/postbacks/sme/processSmePostbacks.js";
import {getFileRowsFromFile} from "../functions/getFileRowsFromFile.js";

export const postbackSmeTask = async (queue) => {
    const {payload} = queue;
    const {fileName, path, mappingConfiguration, splitFile} = payload;

    let totalUpsertedCount = 0;
    let totalModifiedCount = 0;

    if (splitFile) {
        const folderSplit = fileName.replaceAll('.csv', '');
        console.log(`postbackSmeTask processing file for  ${splitFile} start`)
        const fileRows = await getFileRowsFromFile(path, folderSplit, splitFile);
        ({totalUpsertedCount, totalModifiedCount} = await processSmePostbacks(
            mappingConfiguration,
            fileRows,
            fileName,
            totalUpsertedCount,
            totalModifiedCount
        ));
        console.log('fileRows#length: ', fileRows.length)
    }
    return {totalUpsertedCount, totalModifiedCount}
}
