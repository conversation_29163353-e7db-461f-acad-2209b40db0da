export const taskFactory = async (taskName: string) => {
    try {
        const taskModule = await import(`./${taskName}.js`);
        const taskFn = taskModule.default || taskModule[taskName];
        if (typeof taskFn !== 'function') {
            throw new Error(`Task "${taskName}" not found.`);
        }
        return (...args: any[]) => taskFn(...args);
    } catch (error) {
        console.error(`Error to get task:  "${taskName}":`, error);
        throw error;
    }
};
