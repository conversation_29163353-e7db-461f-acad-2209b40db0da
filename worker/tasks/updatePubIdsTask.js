import {connect} from "../mongo/connect";
import {bulkWriteMatchTask} from "../utils/bulkWriteMatchTask.js";

export const updatePubIdsTask = async (queue) => {
    const {db} = await connect()
    const limit = 10000;
    const queryToFindTransfers = {
        $or: [
            {pub_id: ""},
            {pub_id: null},
            {pub_id: {$exists: false}}
        ]
    };

    const getPostbacksToUpdate = await db.collection("postbacks").countDocuments(queryToFindTransfers);

    if (getPostbacksToUpdate > 0) {
        console.log('Postbacks Pub Id to Update: ', getPostbacksToUpdate);
        const totalProcess = Math.ceil(getPostbacksToUpdate / limit)
        let totalPostbacksModified = 0;
        let totalTransfersModified = 0;
        for (let i = 0; i < totalProcess; i++) {
            const postbacks = await db.collection("postbacks")
                .find(queryToFindTransfers).skip(limit * i).limit(limit).toArray();
            const bulkPostbackOps = [];
            const bulkTransferOps = [];
            for (let postback of postbacks) {
                const findLead = await db.collection("leads").findOne({"_id": postback?.lead_id});
                const findTransfer = await db.collection("transfers").findOne({"_id": postback?.transfer_id});
                if (findLead) {
                    const objPBUpdate = {
                        pubid: findLead?.pubid ?? ""
                    };
                    bulkPostbackOps.push({
                        updateOne: {
                            filter: {_id: postback._id},
                            update: {$set: objPBUpdate}
                        }
                    });
                    if (findTransfer) {
                        const objTRUpdate = {
                            pub_id: findLead?.pubid ?? "",
                        };
                        bulkTransferOps.push({
                            updateOne: {
                                filter: {_id: findTransfer._id},
                                update: {$set: objTRUpdate}
                            }
                        });
                    }
                }
            }
            const getTotal = await bulkWriteMatchTask(bulkTransferOps, bulkPostbackOps);
            totalTransfersModified = getTotal.totalTransfersModified;
            totalPostbacksModified = getTotal.totalPostbacksModified;
        }
        return {totalPostbacksModified, totalTransfersModified}
    } else {
        return {totalPostbacksModified: 0, totalTransfersModified: 0}
    }
}
