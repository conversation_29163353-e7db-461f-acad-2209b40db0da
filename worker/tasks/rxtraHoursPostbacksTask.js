import _ from "lodash";
import Decimal from "decimal.js";
import {getFileRowsFromFile} from "../functions/getFileRowsFromFile.js";
import {mappingData} from "../functions/mappings/mappingData.js";
import {generateId} from "../functions/generateId.js";
import {insertMany} from "../mongo/insertMany.js";
import {Collections} from "../mongo/collections.enum.js";


export const rxtraHoursPostbacksTask = async (queue) => {
    const {payload} = queue;
    const {fileName, path, mappingConfiguration, splitFile} = payload;
    let insertedCount = 0;
    if (splitFile) {
        const folderSplit = fileName.replaceAll('.csv', '');
        const fileRows = await getFileRowsFromFile(path, folderSplit, splitFile);
        let mappedData = mapData(fileRows, mappingConfiguration);
        mappedData = groupByDateAndSumDuration(mappedData);
        mappedData = processFileRows(mappedData, mappingConfiguration, fileName);
        const insertedCountResponse = await bulkUpsertData(mappedData);
        insertedCount = insertedCount + insertedCountResponse;
    }
    return {totalUpsertedCount: insertedCount, totalModifiedCount: 0}
}

const bulkUpsertData = async (mappedData) => {
    const insertManyResponse = await insertMany(Collections.Postbacks, mappedData);
    console.log('insertManyResponse#acknowledged & insertedCount: ', insertManyResponse.acknowledged, ',', insertManyResponse.insertedCount);
    return insertManyResponse.insertedCount;
}


const mapData = (fileRows, mappingConfiguration) => {
    return _.map(fileRows, row => {
        return mappingData(mappingConfiguration.mappingConfiguration, row);
    })

}

const processFileRows = (mappedData, mappingConfiguration, fileName) => {
    let constantsToMerge = getConstantsToMerge(mappingConfiguration);
    return mappedData.map((item) => {
        item = mergeConstants(item, constantsToMerge);
        item = assignFileName(item, fileName)
        item = assignTags(item);
        item = assignId(item);
        item = setDateRetained(item)
        return item;
    });
}


const setDateRetained = (item) => {
    item['date_retained'] = item.min_date_retained;
    item = _.omit(item, ['min_date_retained'])
    return item
}
const groupByDateAndSumDuration = (data) => {
    return _(data)
        .groupBy((item) => item.date_retained.toISODate())
        .map((group, date) => {
            const minItem = _.minBy(group, (item) => item.date_retained.toMillis());
            const dateRetained = minItem?.date_retained;
            return {
                date_retained: date,
                hours: new Decimal(_.sumBy(group, "duration"))
                    .div(3600)
                    .toDecimalPlaces(2)
                    .toNumber(),
                min_date_retained: dateRetained,
            };
        })
        .value();
};


const assignId = (item) => {
    item['_id'] = generateId();
    return item;
}


const assignTags = (item) => {
    const hourTypeTag = '02de15b32a8749669eef24bc7aca7648';
    const retainedTag = 'c070f4348e53453db99c326fb94af448';
    item['tags'] = [hourTypeTag, retainedTag];
    return item
}


const assignFileName = (item, fileName) => {
    item['filenames'] = [fileName];
    return item;
}


const mergeConstants = (item, constantsToMerge) => {
    return _.merge(item, constantsToMerge);
}
const getConstantsToMerge = (mappingConfiguration) => {
    const constants = mappingConfiguration.constants;
    let constantToMerge = {}
    _.forEach(constants, constant => {
        constantToMerge = {...constantToMerge, [constant['property']]: constant['value']}
    });
    return constantToMerge;
}
