import _ from "lodash";
import {getFileRowsFromFile} from "../functions/getFileRowsFromFile.js";
import {mappingData} from "../functions/mappings/mappingData.js";
import {getLeads} from "../functions/leads/getLeads.js";
import {generateId} from "../functions/generateId.js";
import {bulkWrite} from "../mongo/bulkWrite.js";
import {Collections} from "../mongo/collections.enum.js";
import {rxtra} from "../functions/taskUpserts/index.js";

export const rxtraPostbacksTask = async (queue) => {
    const {payload} = queue;
    let totalUpsertedCount = 0;
    let totalModifiedCount = 0;
    const {fileName, path, mappingConfiguration, splitFile} = payload;
    if (splitFile) {
        const folderSplit = fileName.replaceAll('.csv', '');
        const fileRows = await getFileRowsFromFile(path, folderSplit, splitFile);
        let mappedData = await processFileRows(fileRows, mappingConfiguration, fileName);
        const upsertResult = await bulkUpsertData(mappedData);
        totalUpsertedCount = totalUpsertedCount + upsertResult.upsertedCount
        totalModifiedCount = totalModifiedCount + upsertResult.modifiedCount
        console.log('rxtra postbacks task end')
    }
    return {totalUpsertedCount, totalModifiedCount}
}


const bulkUpsertData = async (mappedData) => {
    const bulkUpdate = _.map(mappedData, item => {
        const {filter, update} = rxtra(item);
        return {
            updateOne: {
                filter: filter,
                update: update,
                upsert: true
            }
        }
    });
    return await bulkWrite(Collections.Postbacks, bulkUpdate);
}

const processFileRows = (fileRows, mappingConfiguration, fileName) => {
    let constantsToMerge = getConstantsToMerge(mappingConfiguration);
    return Promise.all(fileRows.map(async (row) => {
        let item = mappingData(mappingConfiguration.mappingConfiguration, row);
        item = mergeConstants(item, constantsToMerge);
        item = assignFileName(item, fileName)
        item = assignRetained(item);
        item = assignTags(item);
        item = validateNullValues(item);
        item = assignId(item)
        return await assignVendorIdAndCampaignKey(item);
    }));
}

const assignId = (item) => {
    item['_id'] = generateId();
    return item;
}
const validateNullValues = (item) => {
    item['pubid'] = item['pubid'] != null ? item['pubid'] : '';
    item['subid'] = item['subid'] != null ? item['subid'] : '';
    item['campaign_key'] = item['campaign_key'] != null ? item['campaign_key'] : '';
    return item;
}

const assignTags = (item) => {
    const transferTypeTag = 'a6ebd27ecb924c448901761fb5da8376';
    const retainedTag = 'c070f4348e53453db99c326fb94af448';
    const tags = [transferTypeTag]
    if (item.retained) tags.push(retainedTag);
    item['tags'] = tags;
    return item
}

const assignRetained = (item) => {
    const disposition = item?.disposition
    item['retained'] = disposition === 'CTQ - Email Sent';
    return item;
}

const assignFileName = (item, fileName) => {
    item['filenames'] = [fileName];
    return item;
}

const assignVendorIdAndCampaignKey = async (item) => {
    const {lead_id} = item;
    const leads = await getLeads({_id: lead_id});
    item['vendor_id'] = leads.length > 0 ? leads[0].vendor_id : null;
    item['campaign_key'] = leads.length > 0 ? leads[0].campaign_key : null;
    return item;
}

const mergeConstants = (item, constantsToMerge) => {
    return _.merge(item, constantsToMerge);
}
const getConstantsToMerge = (mappingConfiguration) => {
    const constants = mappingConfiguration.constants;
    let constantToMerge = {}
    _.forEach(constants, constant => {
        constantToMerge = {...constantToMerge, [constant['property']]: constant['value']}
    });
    return constantToMerge;
}
