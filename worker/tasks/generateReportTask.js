import {convertToCsvStream} from "../utils/reports/convertToCsvStream.js";
import {getSort} from "../utils/getSort.js";
import {getNovaItems} from "@conversionfinder/conversion-finder-utils";
import {downloadCSVFromS3} from "../utils/reports/downloadCSVFromS3.js";
import StreamConcat from 'stream-concat';
import {uploadStreamToS3} from "../utils/reports/uploadStreamToS3.js";
import {Readable} from "node:stream";
import {convertToCsv} from "../utils/reports/convertToCsv.js";
import {S3Client} from "@aws-sdk/client-s3";
import {DateTime} from "luxon";

const s3Client = new S3Client({
    region: process.env.S3_REGION,
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY,
        secretAccessKey: process.env.S3_SECRET_KEY
    }
});
const s3Bucket = process.env.S3_BUCKET;

export const generateReportTask = async (queue) => {
    const {payload} = queue;
    const {fileName, email, columns, collection, filters, query, context} = payload;
    console.log('generateReportTask starting: ', email);
    let pageSize = 10000
    let page = 0;
    let hasNextPage = true;
    const sortName = getSort(collection);
    while (hasNextPage) {
        const options = {limit: pageSize, skip: page * pageSize, sort: {[sortName]: -1}};
        const novaFilters = stringToDateFilters(filters);
        const novaPayload = {filters: novaFilters, options, collection, query}
        console.log('novaPayload: ', novaPayload)
        const {response: getRespItems} = await getNovaItems(novaPayload);
        if (getRespItems && getRespItems.length > 0) {
            console.log(`page: ${page}. Response length: ${getRespItems.length}`)
            const headerMapping = Object.keys(columns)
                .filter(key => columns[key].show && columns[key].show === true)
                .map(key => ({original: key, label: columns[key].label}));
            console.log(`writing in file ${fileName}`)
            const existingDataStream = await downloadCSVFromS3(s3Bucket, `storage/public/reports/${fileName}`, s3Client);
            let mergedStream;
            if (existingDataStream) {
                console.log('convertToCsvStream start')
                const newDataStream = await convertToCsvStream(getRespItems, headerMapping);
                console.log('convertToCsvStream end')
                mergedStream = new StreamConcat([existingDataStream, Readable.from('\n'), newDataStream]);
            } else {
                mergedStream = await convertToCsv(getRespItems, headerMapping)
            }
            await uploadStreamToS3(mergedStream, s3Bucket, `storage/public/reports/${payload.fileName}`, s3Client)
            if (getRespItems.length < pageSize) {
                console.log('End report')
                hasNextPage = false;
            }
            page++;
        } else {
            console.log('End report')
            hasNextPage = false;
        }

    }
    return {reportUrl: `${process.env.DATA_BROKER}/public/reports/${fileName}`}
}


const stringToDateFilters = (filters) => {
    const transformedFilters = {...filters};
    Object.keys(filters).forEach(key => {
        const filterValue = filters[key];
        if (filterValue && filterValue['$gte'] && filterValue['$lte']) {
            try {
                const start = DateTime.fromISO(filterValue['$gte'], {zone: "utc"});
                const end = DateTime.fromISO(filterValue['$lte'], {zone: "utc"});
                if (start.isValid && end.isValid) {
                    transformedFilters[key] = {
                        '$gte': start,
                        '$lte': end,
                    };
                }
            } catch (error) {
                console.error(`Error processing date for ${key}:`, error);
            }
        }
    })
    return transformedFilters;
}
