import * as preConfigurationMethods from './index'

export const startPreConfigurations = async (preMethods, data,vendors) => {
    console.log('startPreConfigurations start')
    console.log('preMethods: ',preMethods)
    if (preMethods?.methods && preMethods?.methods.length > 0) {
        for (const method of preMethods.methods) {
            const func = preConfigurationMethods[method];
            if (func) {
                try {
                    data = await func(data, {...preMethods.args, vendors});
                } catch (error) {
                    console.error(`Error executing ${method}:`, error);
                }
            } else {
                console.warn(`Function ${method} not found`);
            }
        }
    }
    return data;
};




