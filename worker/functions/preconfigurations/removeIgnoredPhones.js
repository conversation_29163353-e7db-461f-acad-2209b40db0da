import _ from "lodash";
import {findOne} from "../../mongo/findOne.js";
import {Collections} from "../../mongo/collections.enum.js";


export const removeIgnoredPhones = async (mappedData, args) => {
    const blackListPhonesResult = await findOne(Collections.BlacklistPhones, {collection: 'transfers'})
    if (blackListPhonesResult && blackListPhonesResult.phones && blackListPhonesResult.phones.length > 0) {
        const phones = blackListPhonesResult.phones ? blackListPhonesResult.phones : [];
        return _.filter(mappedData, item =>
            !_.some(phones, num => _.includes(item.transferred_to, num))
        );
    } else {
        return mappedData;
    }

}
