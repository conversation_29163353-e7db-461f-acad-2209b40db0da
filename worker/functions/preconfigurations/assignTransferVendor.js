import {getVendor} from "../transfers/getVendor.js";

export const assignTransferVendor = async (mappedData, args) => {
    console.log('assignTransferVendor start')
    const vendors = args.vendors ? args.vendors : []
    if (mappedData && mappedData.length > 0) {
        mappedData.forEach(item => {
            let vendorFound = getVendor(vendors, item)
            item["vendor_id"] = vendorFound._id;
            item["vendor"] = vendorFound.vendor;
        })
    }
    console.log('assignTransferVendor end')
    return mappedData;
}

