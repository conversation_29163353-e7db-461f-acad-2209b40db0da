import _ from "lodash";
import {mongoConnect} from "../../mongoConnect.js";
import {docmqConfig} from "../../docmq-config.js";

export const assignClientId = async (mappedData, args) => {
    console.log('assignClientId init')
    const {db} = await mongoConnect(docmqConfig.database.database)
    if (mappedData && mappedData.length > 0) {
        const clients = await db.collection('clients').find({}).toArray();
        const unknownClientId = 'f4c7b9589cc24468b21ce0b2fb48e774';
        const internalClientId = '2c4da08245944b59b2c5c7e8b2f0ef22';
        const firstClient = _.head(clients);
        let isClientWithObjectArray = false;
        if (firstClient
            && firstClient.transferred_to
            && _.isArray(firstClient.transferred_to)
            && _.isObject(firstClient.transferred_to[0])) {
            isClientWithObjectArray = true;
        }
        mappedData.forEach(item => {
            let clientFound;
            let tagsFound = [];
            if (isClientWithObjectArray) {
                clientFound = _.find(clients, client =>
                    _.some(client?.transferred_to, {number: item.transferred_to})
                );
                tagsFound = clientFound
                    ? _.get(_.find(clientFound.transferred_to, {number: item.transferred_to}), 'tags', [])
                    : [];
            } else {
                clientFound = clients.find(client => client.transferred_to.includes(item.transferred_to))
            }
            if (!clientFound && item?.transferred_to && item?.transferred_to !== "") {
                clientFound = {_id: unknownClientId}
            } else if (!clientFound && (!item.transferred_to || item.transferred_to === "")) {
                clientFound = {_id: internalClientId}
            }
            item["client_id"] = clientFound ? clientFound._id : null;
            item["tags"] = tagsFound;
        })
    }
    console.log('assignClientId end')
    return mappedData;
}

