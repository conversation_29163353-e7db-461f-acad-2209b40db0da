import _ from "lodash";
import {DateTime} from "luxon";

export const defaultTransfers = (value, fileName) => {
    const now = DateTime.utc();
    const filter = {consumer_phone: value.consumer_phone, client_id: value.client_id};
    const updateFields = {
        ..._.omit(value, "_id", "last_duration", "last_completed_date", "last_call_id",
            "last_lead_id", "last_vendor", "last_campaign_key", "createStrategy"),
        updated_at: now
    };
    const addToSet = {
        calls: {
            duration: value.duration,
            completed_date: value.completed_date,
            call_id: value.call_id
        },
        ...(!value.createStrategy && {
            leads: {
                lead_id: value.lead_id,
                vendor: value.vendor,
                campaign_key: value.campaign_key
            }
        })
    };
    updateFields['campaign_key'] = updateFields['campaign_key'] != null ? updateFields['campaign_key'] : ''
    const update = {
        $setOnInsert: {_id: value._id, created_at: now},
        $set: updateFields,
        $addToSet: addToSet
    };

    return {filter, update}
}

