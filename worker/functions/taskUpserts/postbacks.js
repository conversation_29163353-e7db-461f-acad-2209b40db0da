import {DateTime} from 'luxon';
import _ from 'lodash';

export const postbacks = (value, filename) => {
    const now = DateTime.utc();
    let filter = {};
    let update = [];

    const hasTransferId = _.has(value, 'transfer_id') || value.transfer_id === null || value.transfer_id === ''
    const hasLeadId = _.has(value, 'lead_id') || value.lead_id === null || value.lead_id === ''
    if (hasTransferId) {
        filter = {
            $or: [
                {..._.pick(value, ["transfer_id"])},
                {..._.pick(value, ["lead_id", "client_id", "type"])}
            ]
        }
    } else if (hasLeadId) {
        filter = {..._.pick(value, ["lead_id", "client_id", "type"])};
    } else {
        filter = {..._.pick(value, ["phone", "client_id", "type"])};
    }
    const updateFields = {
        ..._.omit(value, "_id"),
        updated_at: now
    };

    const retainedValue = updateFields['retained']
    const dateRetained = updateFields['date_retained'];
    const durationRetained = updateFields['duration'];
    updateFields['retained'] = {
        $cond: {
            if: {$eq: ["$retained", true]},
            then: "$retained",
            else: retainedValue
        }
    }
    if (updateFields['duration']) {
        updateFields['duration'] = {
            $cond: {
                if: {$eq: ["$retained", true]},
                then: "$duration",
                else: durationRetained
            }
        }
    }

    updateFields['date_retained'] = {
        $cond: {
            if: {$eq: ["$retained", 'Retained']},
            then: "$date_retained",
            else: dateRetained
        }
    }
    updateFields['_id'] = {
        $cond: {
            if: {$eq: [{$type: "$_id"}, "missing"]},
            then: value._id,
            else: "$$REMOVE"
        }
    }
    updateFields['created_at'] = {
        $cond: {
            if: {$eq: [{$type: "$_id"}, "missing"]},
            then: now,
            else: "$created_at"
        }
    }
    updateFields['tags'] = {
        $cond: {
            if: {$not: "$tags"},
            then: value.tags,
            else: {
                $setUnion: [
                    {
                        $cond: {
                            if: {$isArray: "$tags"},
                            then: "$tags",
                            else: ["$tags"]
                        }
                    },
                    value.tags
                ]
            }
        }
    }
    update.push({
        $set: updateFields
    });

    return {filter, update};
};
