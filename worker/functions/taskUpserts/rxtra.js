import {DateTime} from "luxon";
import _ from "lodash";

export const rxtra = (value) => {
    const now = DateTime.utc();
    let update = [];
    const updateFields = {
        ..._.omit(value, "_id"),
        updated_at: now
    };
    const retainedValue = updateFields['retained'];
    const dateRetained = updateFields['date_retained'];
    updateFields['retained'] = {
        $cond: {
            if: {$eq: ["$retained", true]},
            then: "$retained",
            else: retainedValue
        }
    }
    updateFields['date_retained'] = {
        $cond: {
            if: {$eq: ["$retained", 'Retained']},
            then: "$date_retained",
            else: dateRetained
        }
    }
    updateFields['_id'] = {
        $cond: {
            if: {$eq: [{$type: "$_id"}, "missing"]},
            then: value._id,
            else: "$$REMOVE"
        }
    }
    updateFields['created_at'] = {
        $cond: {
            if: {$eq: [{$type: "$_id"}, "missing"]},
            then: now,
            else: "$created_at"
        }
    }
    updateFields['tags'] = {
        $cond: {
            if: {$not: "$tags"},
            then: value.tags,
            else: {
                $setUnion: [
                    {
                        $cond: {
                            if: {$isArray: "$tags"},
                            then: "$tags",
                            else: ["$tags"]
                        }
                    },
                    value.tags
                ]
            }
        }
    }
    update.push({
        $set: updateFields
    });
    return {filter: {lead_id: value.lead_id}, update};
}
