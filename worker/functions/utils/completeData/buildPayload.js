import _ from "lodash";

export const buildPayload = (foreign, updateMapping, updateStaticData) => {
    const payload = {};
    updateMapping.forEach(mappingData => {
        payload[mappingData.localProperty] = foreign[mappingData.foreignPropertyValue]
    })
    if (updateStaticData && !_.isEmpty(updateStaticData)) {
        _.forEach(updateStaticData, (value, key) => {
            payload[key] = value;
        });
    }
    return payload;
}
