import {buildFilters} from "./buildFilters.js";
import _ from "lodash";
import {buildPayload} from "./buildPayload.js";
import {validateRequireConfiguration} from "./validateRequireConfiguration.js";
import {mongoConnect} from "../../../mongoConnect.js";
import {docmqConfig} from "../../../docmq-config.js";

export const completeData = async (configuration) => {
    validateRequireConfiguration(configuration)
    const {
        localTable,
        localFilters,
        foreignTable,
        foreignFilters,
        foreignSort,
        updateMapping,
        limit,
        updateStaticData,
        completeAll,
        offset
    } = configuration
    const {db} = await mongoConnect(docmqConfig.database.database)
    const updateBulkArray = []
    const localQuery = db.collection(localTable).find(localFilters);
    if (limit && limit > 0) {
        localQuery.limit(limit)
    }
    if (offset && offset > 0) {
        const skip = offset * limit;
        localQuery.skip(skip)
    }
    const localResult = await localQuery.toArray();
    console.log('localResult length: ', localResult.length)
    if (localResult && localResult.length > 0) {
        for (const item of localResult) {
            const filters = buildFilters(item, foreignFilters);
            const foreignTableQuery = db.collection(foreignTable).find(filters).sort(foreignSort);
            if (foreignSort && !_.isEmpty(foreignSort)) {
                foreignTableQuery.sort(foreignSort)
            }
            const foreignTableRes = await foreignTableQuery.toArray();

            if (foreignTableRes && foreignTableRes.length > 0) {
                const payload = buildPayload(foreignTableRes[0], updateMapping, updateStaticData)
                const updateOne = {
                    updateOne: {
                        filter: {_id: item._id},
                        update: {$set: payload},
                        upsert: false
                    }
                }
                updateBulkArray.push(updateOne)
            }
        }
        console.log('updateBulkArray#length: ', updateBulkArray.length)
        if (updateBulkArray && updateBulkArray.length > 0) {
            return await db.collection(localTable).bulkWrite(updateBulkArray);
        } else {
            return {message: `0 updates on ${localTable}`};
        }
    } else {
        return {message: `0 rows for ${localTable} with filters: ${localFilters}`}
    }
}
