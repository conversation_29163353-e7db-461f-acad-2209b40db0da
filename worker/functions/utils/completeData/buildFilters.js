import _ from "lodash";

export const buildFilters = (item, foreignFilters) => {
    const filters = _.cloneDeep(foreignFilters);
    replaceKeys(filters,item);
    return filters;
}

const replaceKeys = (obj, item) => {
    _.forOwn(obj, (value, key) => {
        if (typeof value === 'string' && value.includes('localTable.')) {
            const foreignKey = value.replace('localTable.', '');
            obj[key] = item[foreignKey];
        } else if (_.isObject(value)) {
            replaceKeys(value,item);
        } else if (Array.isArray(value)) {
            if (value.length > 0 && typeof value[0] === 'string' && value[0].includes('localTable.')) {
                const foreignKeys = value.map(val => val.replace('localTable.', ''));
                obj[key] = { $in: foreignKeys };
            }
        }
    });
}

