import _ from "lodash";
import {updateMany} from "../../mongo/updateMany.js";
import {getCollectionEnumByValue} from "../../mongo/collections.enum.js";

export const addFilename = async (items, collection, filename) => {
    const ids = _.map(items, '_id');
    const filter = {_id: {$in: ids}};
    const update = {
        $addToSet: {filenames: filename}
    };
    const collectionEnum = getCollectionEnumByValue(collection);
    await updateMany(collectionEnum, filter, update);
}
