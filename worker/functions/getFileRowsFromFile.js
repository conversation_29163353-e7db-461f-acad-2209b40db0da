import {tryit} from "radash";
import axios from "axios";
import {parseCsvStream} from "./stream/parseCsvStream.js";
import {docmqConfig} from "../docmq-config.js";

export const getFileRowsFromFile = async (path, folderSplit, splitFile) => {
    console.log(`Processing file ${splitFile} start`)
    const [error, response] = await tryit(axios)({
        url: `${docmqConfig.databroker.uri}/public/${path}/${folderSplit}/${splitFile}`,
        method: 'GET',
        responseType: 'stream',
    });
    if (error) {
        console.error(`Error fetching file: ${splitFile}`, error);
        return;
    }
    const stream = response.data;
    return await parseCsvStream(stream, 0, "£");
}
