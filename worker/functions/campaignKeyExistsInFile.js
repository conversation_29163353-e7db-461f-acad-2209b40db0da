import _ from "lodash";

export const campaignKeyExistsInFile = (fileRows) => {
    console.log('verify if campaign key exists in file')
    if (fileRows && fileRows.length > 0) {
        const campaignKeyRegex = /^[a-f0-9]{32}$/;
        const keys = Object.keys(fileRows[0]);
        const result = _.find(keys, key => campaignKeyRegex.test(key));
        console.log('hasCampaignKey: ', result != null && result !== '');
        console.log('result: ', result);
        return {hasCampaignKey: result != null && result !== '', campaignKey: result};
    } else {
        return {hasCampaignKey: false, campaignKey: ''};
    }
}
