import _ from "lodash";
import {transformDate} from "./transformDate";
import {transformNumeric} from "./transformNumeric";
import {transformToArray} from "./transformToArray.js";
import {phoneFormat} from "./phoneFormat.js";

export const mappingData = (mappingConfiguration, originalData) => {
    try {
        const transformedData = {};
        _.forEach(mappingConfiguration, (value, key) => {
            if (value.transformer !== 'ignore') {
                switch (value.transformer) {
                    case 'date':
                        transformedData[value.output] = transformDate(value, originalData[key]);
                        break;
                    case 'numeric':
                        transformedData[value.output] = transformNumeric(value, originalData[key]);
                        break;
                    case 'toArray':
                        transformedData[value.output] = transformToArray(value, originalData);
                        break;
                    case 'phone':
                        transformedData[value.output] = phoneFormat(value, originalData[key]);
                        break;
                    default:
                        transformedData[value.output] = originalData[key];
                        break;
                }
                if (!transformedData[value.output] || transformedData[value.output] === '') {
                    transformedData[value.output] = value.default != null ? value.default : null;
                }
            }
        });
        return transformedData;
    } catch (e) {
        console.error('Error mapping: ', originalData);
        console.error(e);
        return {};
    }
};
