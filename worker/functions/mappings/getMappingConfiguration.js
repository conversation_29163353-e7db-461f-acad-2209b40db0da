
export const getMappingConfiguration = (mappingConfiguration) => {
    const mappingColumnsConfiguration = mappingConfiguration.columns;
    console.assert(mappingColumnsConfiguration, 'columns configuration not found')
    if (!mappingColumnsConfiguration) return;
    const constMappingConfiguration = mappingConfiguration?.constants ? mappingConfiguration?.constants : {};
    const dbConfig = mappingConfiguration.db_config;
    console.assert(dbConfig, 'No data base configuration')
    console.assert(dbConfig?.table_name, 'Table name in data base configuration not found')
    console.assert(dbConfig?.data_base_connection, 'No data base connection type in data base configuration not found')
    const callbacks = mappingConfiguration.callbacks ? mappingConfiguration.callbacks : []
    const preConfigurationMethods = mappingConfiguration.preConfigurationMethods ? mappingConfiguration.preConfigurationMethods : []
    const processMethod = mappingConfiguration.processMethod ? mappingConfiguration.processMethod : 'processDefault'
    const completeProcessMethod = mappingConfiguration.completeProcessMethod
    return {
        mappingColumnsConfiguration,
        constMappingConfiguration,
        dbConfig,
        callbacks,
        preConfigurationMethods,
        processMethod,
        completeProcessMethod
    };
}
