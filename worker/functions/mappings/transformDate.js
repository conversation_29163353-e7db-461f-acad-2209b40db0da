import {DateTime} from "luxon";
import _ from "lodash";

export const transformDate = (mappingConfiguration, value) => {
    if (value) {
        let transformerConfig = mappingConfiguration.transformer_config;
        if (typeof transformerConfig === 'string') {
            transformerConfig = JSON.parse(transformerConfig);
        }
        try {
            const fromFormats = transformerConfig.fromFormats;
            let parsedDate = null;
            const isValidDate = _.some(fromFormats, (format, index) => {
                parsedDate = DateTime.fromFormat(value, format);
                return !!parsedDate.isValid;
            });
            if (!isValidDate) parsedDate = null;
            if (parsedDate != null) {
                if (transformerConfig?.sourceTimeZone && transformerConfig?.sourceTimeZone !== 'utc') {
                    parsedDate = parsedDate.setZone(transformerConfig.sourceTimeZone, {keepLocalTime: true});
                    parsedDate = parsedDate.toUTC();
                } else {
                    parsedDate = parsedDate.setZone('utc', {keepLocalTime: true});
                }
            }
            return parsedDate;
        } catch (error) {
            console.log('transformerConfig: ', transformerConfig)
            console.log('value: ', value)
            console.error("Date parsing error:", error);
            return null;
        }

    } else {
        return null
    }
}

