import {findAllByFilter} from "../../mongo/findAllByFilter.js";
import {Collections} from "../../mongo/collections.enum.js";

export const getLeadsWithCorrectLeadId = async (groupedTransfers) => {
    const transfersWithLeadsIds = new Set(groupedTransfers.transfersWithLeadsIds || []);
    const transfers = transfersWithLeadsIds.size > 0 ? Array.from(transfersWithLeadsIds) : [];
    const leadIds = transfers.map(transfer => transfer.lead_id);
    const filter = {_id: {$in: leadIds}}
    return await findAllByFilter(Collections.Leads, filter, {sort: {date_created: -1}})

}
