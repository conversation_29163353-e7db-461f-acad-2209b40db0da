import _ from "lodash";
import {mongoConnect} from "../../mongoConnect.js";
import {docmqConfig} from "../../docmq-config.js";


export const getLeadsWithoutCorrectLeadId = async (groupedTransfers) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    const transferWithoutLeadsIds = new Set(groupedTransfers.transferWithoutLeadsIds || []);
    const transfers = transferWithoutLeadsIds.size > 0 ? Array.from(transferWithoutLeadsIds) : [];
    const uniquePhones = new Set(transfers.map(item => item.consumer_phone));
    const uniqueEmails = new Set(_.compact(transfers.map(item => item.email)));
    const filter = {
        $or: [
            {phone: {$in: Array.from(uniquePhones)}},
            ...(uniqueEmails ? [{email: {$in: Array.from(uniqueEmails)}}] : [])
        ]
    };
    return await db.collection('leads')
        .find(filter)
        .sort({date_created: -1})
        .toArray();

}
