import {fillVendorId, fillVendorName} from "../vendors/vendorsStaticValues.js";
import {insertLeads} from "../leads/insertLeads.js";


export const createTemporalLead = async (temporalLeads, args) => {
    const {defaultVendorKey} = args;
    if (temporalLeads && temporalLeads.length > 0) {
        const leadsInsert = temporalLeads.map(temporalLead => {
            return {
                _id: temporalLead.lead_id,
                phone: temporalLead.consumer_phone,
                date_created: temporalLead.completed_date,
                campaign_key: temporalLead?.campaign_key != null ? temporalLead.campaign_key : '',
                vendor_key: defaultVendorKey ? defaultVendorKey : 'unknown',
                vendor: fillVendorName,
                vendor_id: fillVendorId,
                email: temporalLead.email,
                subid: temporalLead.sub_id != null ? temporalLead.sub_id : '',
                pubid: temporalLead.pub_id != null ? temporalLead.pub_id : ''
            }
        });
        await insertLeads(leadsInsert);
    }
}
