import _ from "lodash";
import {fillVendorId, fillVendorName, unknownVendorId} from "../vendors/vendorsStaticValues.js";
import {createTemporalLead} from "./createTemporalLead.js";

export const assignMissingInformationToTransfersWithCorrectLeadId = async (mappedData, leadsMap, leadsWithoutCorrectLeadId, args) => {
    const processedData = _.map(mappedData, item => {
        if (!(item && item.lead_id && item.lead_id.length === 32)) {
            return item;
        }

        if (item.vendor_id !== fillVendorId && item.vendor_id !== unknownVendorId) {
            return item;
        }
        const leadInfo = findLeadInfo(item, leadsMap, leadsWithoutCorrectLeadId);
        if (leadInfo) {
            item.vendor = leadInfo.vendor;
            item.vendor_id = leadInfo.vendor_id;
        } else {
            item.vendor = fillVendorName;
            item.vendor_id = fillVendorId;
            console.info(`Lead with lead_id: ${item.lead_id} not found. Creating lead.`);
        }
        return item;
    });

    const temporalLeads = _.filter(processedData, item =>
        item.lead_id?.length === 32 &&
        item.vendor_id === fillVendorId &&
        item.vendor === fillVendorName
    );
    if (temporalLeads.length > 0) {
        await createTemporalLead(_.uniq(temporalLeads), args);
    }
    return processedData;
};

const findLeadInfo = (item, leadsMap, leadsWithoutCorrectLeadId) => {
    const leadById = _.find(leadsMap, lead => lead._id === item.lead_id);
    if (leadById) return leadById;
    return findLeadByPhoneOrEmail(leadsWithoutCorrectLeadId, item);
};

const findLeadByPhoneOrEmail = (leads, item) => {
    const filteredLeads = _.filter(leads, lead =>
        lead.phone === item.consumer_phone || lead.email === item.email
    );
    return filteredLeads.length > 0
        ? _.maxBy(filteredLeads, lead => new Date(lead.date_created))
        : null;
};
