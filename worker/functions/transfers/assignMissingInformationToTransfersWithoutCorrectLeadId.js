import {createTemporalLead} from "./createTemporalLead.js";
import {generateId} from "../generateId.js";
import {orderLeads} from "../leads/orderLeads.js";
import {fillVendorId, fillVendorName} from "../vendors/vendorsStaticValues.js";



export const assignMissingInformationToTransfersWithoutCorrectLeadId = async (mappedData, leadsMap, args) => {
    const orderedLeadsMap = orderLeads(leadsMap);
    if (!mappedData) {
        console.warn("mappedData is null or undefined, returning empty array");
        return [];
    }
    const {processedData, temporalLeads} = processTransfersWithoutCorrectLeadId(mappedData, orderedLeadsMap);
    if (temporalLeads.length > 0) {
        await createTemporalLead(temporalLeads, args);
    }
    return processedData;
};

const processTransfersWithoutCorrectLeadId = (mappedData, leadsMap) => {
    const temporalLeads = [];
    const processedData = mappedData.map(item => {
        if (!(item && item.lead_id && item.lead_id.length < 32)) {
            return item;
        }
        const leadInfo = findLeadInfo(item, leadsMap);
        if (leadInfo) {
            item.vendor = leadInfo.vendor || fillVendorName;
            item.vendor_id = leadInfo.vendor_id || fillVendorId;
            item.lead_id = leadInfo._id;
        } else {
            item.vendor = fillVendorName;
            item.vendor_id = fillVendorId;
            item.lead_id = generateId();
            console.info(`Lead with phone: ${item.consumer_phone} and email ${item.email} not found.`);
            console.info(`Creating temporal lead`);
            temporalLeads.push(item);
        }

        return item;
    });
    return {processedData, temporalLeads};
};


const findLeadInfo = (item, leadsMap) => {
    return leadsMap[item.consumer_phone] || leadsMap[item.email] || null;
};
