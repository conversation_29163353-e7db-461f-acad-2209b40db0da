import _ from "lodash";

export const getVendor = (vendors, item) => {
    let vendorFound = vendors.find(vendor => vendor.vendor !== 'unknown'
        && vendor.vendor === safeTrim(item.vendor))
    if (_.get(vendors, '[0].pub_ids[0]', false) && _.isObject(vendors[0].pub_ids[0])) {
        vendorFound = getVendorByObjectPubId(vendorFound, vendors, item)
    } else {
        vendorFound = getVendorByArrayPubId(vendorFound, vendors, item);
    }
    if (!vendorFound) {
        vendorFound = vendors.find(vendor => vendor.vendor === 'unknown')
    }
    return vendorFound;
}

const getVendorByObjectPubId = (vendorFound, vendors, item) => {
    if (!vendorFound) {
        vendorFound = vendors.find(vendor => Array.isArray(vendor.pub_ids) && vendor.pub_ids.some(pub => pub.id === item.pub_id));
    }
    if (!vendorFound) {
        vendorFound = vendors.find(vendor => Array.isArray(vendor.pub_ids) && vendor.pub_ids.some(pub => pub.id === item.pubid));
    }
    return vendorFound
}

const getVendorByArrayPubId = (vendorFound, vendors, item) => {
    if (!vendorFound) {
        vendorFound = vendors.find(vendor => Array.isArray(vendor.pub_ids) && vendor.pub_ids.includes(safeTrim(item.pub_id)));
    }
    if (!vendorFound) {
        vendorFound = vendors.find(vendor => Array.isArray(vendor.pub_ids) && vendor.pub_ids.includes(safeTrim(item.pubid)));
    }

    return vendorFound;
}

const safeTrim = (value) => {
    return typeof value === 'string' ? value.trim() : value;
};
