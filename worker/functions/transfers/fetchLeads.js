import _ from "lodash";
import {getLeadsWithCorrectLeadId} from "./getLeadsWithCorrectLeadId.js";
import {getLeadsWithoutCorrectLeadId} from "./getLeadsWithoutCorrectLeadId.js";

export const fetchLeads = async (mappedData,) => {
    const groupedTransfers = _.groupBy(mappedData, item => item && item.lead_id && item.lead_id.length === 32
        ? 'transfersWithLeadsIds'
        : 'transferWithoutLeadsIds');
    const leadsWithCorrectLeadIds = await getLeadsWithCorrectLeadId(groupedTransfers.transfersWithLeadsIds);
    const leadsWithoutCorrectLeadId = await getLeadsWithoutCorrectLeadId(mappedData);
    return {leadsWithCorrectLeadIds, leadsWithoutCorrectLeadId};
}

