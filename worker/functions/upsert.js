import * as upsertConfiguration from './taskUpserts/index.js'
import {mongoConnect} from "../mongoConnect.js";
import {docmqConfig} from "../docmq-config.js";

export const upsert = async (table, items, configuration, upsert = true, importFilename) => {
    const {db} = await mongoConnect(docmqConfig.database.database)
    try {
        console.log('Start transaction upsert')
        if (!configuration) {
            console.error("No config mongo for filter and update");
            return;
        }
        const collection = db.collection(table);
        let updateBulk = items.map((value) => {
            const configFunc = upsertConfiguration[configuration];
            const {filter, update} = configFunc(value, importFilename);
            return {
                updateOne: {
                    filter: filter,
                    update: update,
                    upsert: upsert
                }
            }
        });
        return await collection.bulkWrite(updateBulk);

    } catch (e) {
        console.log('error to upsert: ', e)
        throw e;
    }
};
