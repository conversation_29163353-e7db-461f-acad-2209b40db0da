import Decimal from "decimal.js";
import _ from "lodash";

export const getDecimalAmountByEventByLabel = (invoiceAmountType, items, amount) => {
    const decimalAmount = new Decimal(amount);
    const eventsDecimal = new Decimal(items)
    if (_.isEqual(invoiceAmountType, 'all')) {
        return decimalAmount.div(eventsDecimal).toDP(2).mul(100).toNumber()
    } else {
        return decimalAmount.toDP(2).mul(100).toNumber();
    }
}
