import * as csv from "fast-csv";

export const parseCsvStream = (stream, skipLines = 0, delimiter = ',') => {
    let rowsArray = []
    return new Promise((resolve, reject) => {
        stream.pipe(csv.parse({headers: true, skipLines, delimiter}))
            .on('error', (error) => {
                console.log('Error to read stream')
                return reject(error)
            })
            .on('data', (row) => {
                const trimmedRow = {};
                for (const key in row) {
                    if (row.hasOwnProperty(key)) {
                        const trimmedKey = key.trim();
                        trimmedRow[trimmedKey] = row[key];
                    }
                }
                rowsArray.push(trimmedRow);
            })
            .on('end', () => {
                console.log(`Parsed ${rowsArray.length} rows!`)
                return resolve(rowsArray)
            })
    })
}
