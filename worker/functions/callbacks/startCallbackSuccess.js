import * as successCallbacks from './index.js';

export const startCallbackSuccess = async (callbacks,mappedData) => {
    if (callbacks && callbacks.length > 0) {
        try {
            await Promise.all(callbacks.map(async (successCallback) => {
                const func = successCallbacks[successCallback.method];
                if (func) {
                    const callbackResult = await func(successCallback.configuration,mappedData);
                    console.log(`callback method ${successCallback.method} result`);
                } else {
                    console.warn(`Success function ${successCallback.method} not found`)
                }
            }))
        } catch (error) {
            console.error('Error to execute success callbacks');
            console.error(error);
        }
    }

}
