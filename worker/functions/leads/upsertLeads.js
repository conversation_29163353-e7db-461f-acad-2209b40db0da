import _ from "lodash";
import {bulkWrite} from "../../mongo/bulkWrite.js";
import {Collections} from "../../mongo/collections.enum.js";


export const upsertLeads = async (leadsToInsert) => {
    const bulk = _.map(leadsToInsert, lead => {
        return {
            updateOne: {
                filter: {vendor_id: lead.vendor_id, phone: lead.phone},
                update: {
                    $setOnInsert: {_id: lead._id},
                    $set: _.omit(lead, "_id")
                },
                upsert: true
            }
        }
    })
    if (bulk && bulk.length > 0) {
        await bulkWrite(Collections.Leads, bulk);
    }
}
