import _ from "lodash";
import {bulkWrite} from "../../mongo/bulkWrite.js";
import {Collections} from "../../mongo/collections.enum.js";

export const insertLeads = async (leadsToInsert) => {
    const operations = _.map(leadsToInsert, lead => ({
        updateOne: {
            filter: {_id: lead._id},
            update: {$set: lead},
            upsert: true
        }
    }));
    await bulkWrite(Collections.Leads, operations);
}
