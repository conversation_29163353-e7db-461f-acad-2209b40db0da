import {blueModo} from "./blueModo";
import {citizens} from "./citizens";
import {ecp} from "./ecp";
import {kirkendall} from "./kirkendall";
import {premier} from "./premier";
import {trajector} from "./trajector";


export const postbacksMethods = {
    "a29aa07eba694eb6b12420103edb87f9": blueModo,
    "71f9999b04924b3e9c107f89c611be75": citizens,
    "974083936c5f43e3999526d2fbf0745c": ecp,
    "2f2bbbe85665414eace6f0d62e92d9c9": kirkend<PERSON>,
    "98f37ac98ce34c13ae570fbd4732cee4": premier,
    "87f37ac98ce34c13ae570fbd4732cee4": trajector,
}
