export const premier = (item, type) => {
    if (type === "call") {
        item['duration'] = timeToSeconds(item['duration']);
        return {
            retained: item['duration'] && item['duration'] > 120 ? true : false,
        };
    } else {
        return {retained: item['date_retained'] && item['date_retained'] !== '' || item['status'] === 'Sign Up Package' ? true : false}
    }

}

function timeToSeconds(timeString) {
    const [hours, minutes, seconds] = timeString.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds;
}
