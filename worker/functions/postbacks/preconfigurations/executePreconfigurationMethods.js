import * as preConfigurationFunctions from "./index.js";

export const executePreconfigurationMethods = async (mappedData, preConfigurationMethods) => {
    if (preConfigurationMethods && preConfigurationMethods.length > 0) {
        for (let preConfigMethod of preConfigurationMethods) {
            const method = preConfigurationFunctions[preConfigMethod];
            if (method) {
                mappedData = await method(mappedData);
            }
        }
    }
    return mappedData;
}
