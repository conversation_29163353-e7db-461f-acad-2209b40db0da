import _ from "lodash";

export const assignTags = (mappedData) => {
    const tagsKeys = {
        "retained": "c070f4348e53453db99c326fb94af448",
        "retainer": "6d7a5b27e80c4a9cb0713d7b2a4691a5",
        "transfer": "a6ebd27ecb924c448901761fb5da8376",
        "call": "8e721409b8a4495c91c82e018ea5a833"
    }
    return _.map(mappedData, (item) => {
        let tags = item.tags || [];
        if (item.retained && item.retained === true) {
            tags = _.union(tags, [tagsKeys.retained]);
        }
        const typeTag = tagsKeys[item.type];
        if (typeTag) {
            tags = _.union(tags, [typeTag]);
        }
        return {...item, tags};
    });
}
