import _ from "lodash";
import {findAllByFilter} from "../../../mongo/findAllByFilter.js";
import {Collections} from "../../../mongo/collections.enum.js";

export const assignAgeTag = async (mappedData) => {
    // bf63c242d3504a94bc7981870a239832: age between 55-63
    // a8bbba180e9e4a3b96c338afc4756695: age between 50-54
    const tags = await findAllByFilter(Collections.Tags, {_id: {$in: ['bf63c242d3504a94bc7981870a239832', 'a8bbba180e9e4a3b96c338afc4756695']}});
    console.assert(tags.length >= 2, `No tags found. Only found: ${JSON.stringify(tags)}`)
    if (tags && tags.length >= 2) {
        const objectTags = _.reduce(tags, (result, tag) => {
            result[tag.name] = tag._id;
            return result;
        }, {});
        return _.map(mappedData, (item) => {
            const age = item.age;
            let tag = objectTags['55-63']
            if (age) {
                if (age >= 50 && age < 55) {
                    tag = objectTags['50-54']
                }
            }
            const existingTags = item['tags'] && item['tags'] != null ? item['tags'] : [];
            existingTags.push(tag);
            item['tags'] = existingTags;
            return item;
        })
    } else {
        return mappedData;
    }
}
