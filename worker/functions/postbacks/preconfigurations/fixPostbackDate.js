import _ from "lodash";

export const fixPostbackDate = (mappedData) => {
    return _.map(mappedData, (item)=>{
        if (item.retained != null
            && item.retained === true
            && item['engagement_date']
            && item['engagement_date'] !== '') {
            item['date_retained'] = item['engagement_date']
        }
        if (item['date_retained'] == null || item['date_retained'] === '') {
            item['date_retained'] = item['matter_created_date']
        }
        item = _.omit(item, 'engagement_date');
        item = _.omit(item, 'matter_created_date');
        return item;
    })

}
