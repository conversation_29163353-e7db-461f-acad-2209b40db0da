import {postbacksMethods} from "./retained/index.js";
import {verifyRequiredFields} from "./commonsFunctions/verifyRequiredFields.js";
import {getClients} from "./commonsFunctions/getClients.js";
import {preLoadData} from "./commonsFunctions/preLoadData.js";
import _ from "lodash";
import {getTransfers} from "./commonsFunctions/getTransfers.js";
import {executePreconfigurationMethods} from "./preconfigurations/executePreconfigurationMethods.js";
import {createPostbackLead} from "./commonsFunctions/createPostbackLead.js";
import {upsertData} from "../upsertData";
import {fillVendorId, fillVendorName} from "../vendors/vendorsStaticValues.js";
import {getLeads} from "../leads/getLeads.js";

export const processPostbacks = async (postbackFileConfiguration, client, fileRows,
                                       fileName, type, totalUpsertedCount,
                                       totalModifiedCount) => {
    console.log('start process postbacks')
    const collection = postbackFileConfiguration.mongo.collection;
    if (!postbackFileConfiguration) {
        console.error("postbackFileConfiguration not found")
        return;
    }
    const postbackFunction = postbacksMethods[client];
    const hasRequiredFields = verifyRequiredFields(postbackFileConfiguration.requiredKeys, fileRows);
    if (!hasRequiredFields) {
        console.error("Required fields not found")
        throw {
            error: "Required fields not found",
            detail: `required fields: ${postbackFileConfiguration.requiredKeys}, data: ${postbackFileConfiguration[0]}`
        }
    }
    if (postbackFunction != null) {
        console.log(`postback function found`)
        const clientFound = await getClients({_id: postbackFileConfiguration.mongoId});
        const clientId = clientFound && clientFound.length > 0 ? clientFound[0]._id : ''
        let mappedData = preLoadData(fileRows, postbackFileConfiguration.mappingConfiguration, client, clientId, type);
        mappedData = excludeEmptyItems(mappedData);
        console.log('complete data start')
        let {completedData} = await completePostbackData(postbackFileConfiguration, mappedData, clientId);
        console.log('complete data end');
        console.log('executePreConfigMethods start')
        completedData = await executePreConfigMethods(completedData, postbackFileConfiguration);
        console.log('executePreConfigMethods end')
        const {completedItems, itemsRetainedWithoutPhone} = excludeItemsWithoutPhone(completedData);
        console.log('itemsRetainedWithoutPhone size: ', itemsRetainedWithoutPhone.length);
        console.log('completedItems size: ', completedItems.length);
        const itemsProcessed = await processItemsRetainedWithoutPhone(itemsRetainedWithoutPhone);
        completedData = _.concat(completedItems, itemsProcessed);
        console.log(`Starting upsertTask completedData length: `, completedData.length);
        return await upsertData(collection, completedData, postbackFileConfiguration.mongo.upsertConfiguration,
            fileName, totalUpsertedCount, totalModifiedCount, postbackFileConfiguration.mappingConfiguration)
    } else {
        console.error('Postback method not found');
        return {totalUpsertedCount: 0, totalModifiedCount: 0};
    }
}

const processItemsRetainedWithoutPhone = async (itemsRetainedWithoutPhone) => {
    return await Promise.all(
        _.map(itemsRetainedWithoutPhone, async (item) => {
            await createPostbackLead(item)
            item['lead_id'] = item['phone'];
            item['vendor'] = fillVendorName;
            item['vendor_id'] = fillVendorId;
            return item;
        })
    );
};

const excludeEmptyItems = (mappedData) => {
    return mappedData.filter((item) => {
        return !_.isEmpty(item)
    });
}

const excludeItemsWithoutPhone = (mappedData) => {
    const completedItems = [];
    const itemsRetainedWithoutPhone = [];
    _.each(mappedData, (item) => {
        if (item.phone && item.phone !== '') {
            completedItems.push(item);
        }
        if (!item.found && item.retained && item.phone) {
            itemsRetainedWithoutPhone.push(item);
        }
    })
    return {completedItems, itemsRetainedWithoutPhone};
};

const completePostbackData = async (postbackFileConfiguration, mappedData, clientId) => {
    console.log(`completePostbackData:start. Methods: ${postbackFileConfiguration.completeDataMethods.length}`);
    for (let completeMethod of postbackFileConfiguration?.completeDataMethods) {
        const filterInValues = _.map(mappedData, completeMethod.localProperty).filter(value => value != null && value !== '');
        const filters = {[completeMethod.foreignProperty]: {$in: filterInValues}};
        const mappedDataNotFound = mappedData.filter(item => !item.found);
        if (mappedDataNotFound.length > 0) {
            let dataToFetch;
            if (completeMethod.collection === 'transfers') {
                filters['client_id'] = clientId;
                dataToFetch = getTransfers({
                    ...filters, $and: [
                        {transferred_to: {$ne: ''}},
                        {transferred_to: {$ne: null}}
                    ]
                });
            } else if (completeMethod.collection === 'leads') {
                dataToFetch = getLeads(filters);
            }
            if (dataToFetch) {
                const fetchedData = await dataToFetch;
                const dataMap = new Map(fetchedData.map(item => [item[completeMethod.foreignProperty], item]));
                await Promise.all(mappedData.map(async item => {
                    if (!item.found) {
                        const foundItem = dataMap.get(item[completeMethod.localProperty]);
                        let pubid = ''
                        let subid = ''
                        if (foundItem) {
                            if (completeMethod.collection === 'transfers') {
                                item['lead_id'] = foundItem.lead_id;
                                item['transfer_id'] = foundItem._id;
                                item['campaign_key'] = foundItem.campaign_key != null ? foundItem.campaign_key : '';
                                item['tags'] = foundItem.tags ? foundItem.tags : [];
                                pubid = foundItem.pub_id;
                                subid = foundItem.sub_id;
                            } else if (completeMethod.collection === 'leads') {
                                item['lead_id'] = foundItem._id;
                                pubid = foundItem.pubid;
                                subid = foundItem.subid;
                                item['campaign_key'] = item.campaign_key != null ? item.campaign_key : '';
                                item['tags'] = [];
                            }
                            if (foundItem.vendor_id) {
                                item['vendor_id'] = foundItem.vendor_id;
                            }
                            item['found'] = true;
                        }
                        item['pubid'] = pubid != null ? pubid : '';
                        item['subid'] = subid != null ? subid : '';
                    }
                }));
            }
        }
    }
    return {completedData: mappedData};
}

const executePreConfigMethods = async (mappedData, postbackFileConfiguration) => {
    return await executePreconfigurationMethods(mappedData, postbackFileConfiguration.preConfigurationMethods)
}
