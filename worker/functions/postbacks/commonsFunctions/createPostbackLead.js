import {insertOne} from "../../../mongo/insertOne.js";
import {Collections} from "../../../mongo/collections.enum.js";

export const createPostbackLead = async (item) => {
    const phone = item['phone'];
    const lead =
        {
            _id: phone,
            phone,
            subid: item.subid,
            pubid: item.pubid,
            campaign_key: item?.campaign_key != null ? item?.campaign_key : ''
        };
    await insertOne(Collections.Leads, lead);
}
