import _ from "lodash";
import {postbacksMethods} from "../retained/index.js";
import {generateId} from "../../generateId";
import {mappingData} from "../../mappings/mappingData.js";

export const preLoadData = (fileRows, mappingColumnsConfiguration, client, clientId, type) => {
    const postbackFunction = postbacksMethods[client];
    const mappedData = _.map(fileRows, (item) => {
        let mappedItem = mappingData(mappingColumnsConfiguration, item);
        mappedItem['_id'] = generateId()
        mappedItem['postback_client'] = client;
        mappedItem['found'] = false;
        mappedItem['client_id'] = clientId
        const {retained} = postbackFunction(mappedItem, type);
        mappedItem['retained'] = retained;
        mappedItem['type'] = type;
        mappedItem['phone'] = mappedItem['phone'] && mappedItem['phone'] !== ''
            ? mappedItem['phone'] : mappedItem['secondary_phone']
        return mappedItem;
    });
    return _.orderBy(mappedData, ['postback_date'], ['asc']);
}
