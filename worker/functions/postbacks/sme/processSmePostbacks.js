import _ from "lodash";
import {generateId} from "../../generateId.js";
import {mergeAdditionalData} from "../../mergeAdditionalData.js";
import {upsertData} from "../../upsertData.js";
import {assignTags} from "../preconfigurations/index.js";
import {getTransfers} from "../commonsFunctions/getTransfers.js";
import {campaignKeyExistsInFile} from "../../campaignKeyExistsInFile.js";
import {assignCampaignKey} from "../../assignCampaignKey.js";
import {parallel} from "radash";
import {mappingData} from "../../mappings/mappingData.js";
import {getLeads} from "../../leads/getLeads.js";

export const processSmePostbacks = async (postbackFileConfiguration, fileRows,
                                          fileName, totalUpsertedCount,
                                          totalModifiedCount) => {
    console.log('start process sme postbacks')
    const collection = 'postbacks';
    if (!postbackFileConfiguration) {
        console.error("smePostbackFileConfiguration not found")
        return;
    }
    let mappingColumnsConfiguration = postbackFileConfiguration.mappingConfiguration;
    let constMappingConfiguration = postbackFileConfiguration.constants;
    let upsertConfiguration = postbackFileConfiguration.mongo.upsertConfiguration;
    const {hasCampaignKey, campaignKey} = campaignKeyExistsInFile(fileRows);
    let mappedData = _.map(fileRows, (item) => {
        let mappedItem = mappingData(mappingColumnsConfiguration, item);
        mappedItem['_id'] = generateId()
        mappedItem['found'] = false;
        mappedItem['retained'] = (item.disposition?.toLowerCase() || "").includes('new')
        return mappedItem;
    });
    mappedData = mergeAdditionalData(mappedData, constMappingConfiguration);
    mappedData = assignTags(mappedData);
    if (hasCampaignKey) assignCampaignKey(mappedData, campaignKey)
    mappedData = await completeDataWithTransfersAndLeads(mappedData)
    console.log(`Starting upsertTask completedData length: `, mappedData.length);
    return await upsertData(collection, mappedData, upsertConfiguration,
        fileName, totalUpsertedCount, totalModifiedCount, mappingColumnsConfiguration)
}

const completeDataWithTransfersAndLeads = async (mappedData) => {
    const {emails, phones, leads} = _.reduce(mappedData, (result, item) => {
        if (item.email) result.emails.add(item.email);
        if (item.phone) result.phones.add(item.phone);
        if (item.lead_id) result.leads.add(item.lead_id);
        return result;
    }, {emails: new Set(), phones: new Set(), leads: new Set()});
    const emailsArray = [...emails];
    const phonesArray = [...phones];
    const leadsArray = [...leads];
    const clientId = mappedData[0].client_id;
    const campaignKey = mappedData[0].campaign_key;
    const {transfersFound, leadsFound} = await findData(phonesArray, emailsArray, leadsArray, campaignKey, clientId);
    for (let item of mappedData) {
        let transferFound = false;
        const lead = leadsFound && leadsFound.find(lead => lead.phone === item.phone || lead.email === item.email || lead._id === item.lead_id);
        const { leadId, valid } = getLeadId(item, lead);
        let leadIdFound = leadId;
        if (valid) {
            transferFound = findTransferWithLeadId(transferFound, leadId) || findTransferWithPhoneOrEmail(transfersFound, item);
        } else {
            transferFound = findTransferWithPhoneOrEmail();
        }
        if (transferFound) {
            leadIdFound = transferFound.lead_id ?? leadIdFound;
        }
        if (transferFound || lead) {
            item.found = true;
            item.vendor_id = transferFound?.vendor_id || lead?.vendor_id || '';
        }
        item.lead_id = leadIdFound;
        item['tags'] = applyTags(item, transferFound, lead);
        if (transferFound) item['transfer_id'] = transferFound._id;
    }
    return mappedData;
}
const findTransferWithLeadId = (transfersFound, leadId) => {
    return transfersFound && transfersFound.find(transfer => transfer.lead_id === leadId);
}

const findTransferWithPhoneOrEmail = (transfersFound, item) => {
    return transfersFound && transfersFound.find(transfer => transfer.consumer_phone === item.phone || transfer.email === item.email);
}
const getLeadId = (item, lead) => {
    let leadId = item.lead_id;
    if (leadId && leadId.length === 32) {
        return {leadId, valid: true};
    } else if (lead && lead._id) {
        return {leadId: lead._id, valid: true};
    } else {
        return {leadId, valid: false}
    }
}

const applyTags = (item, transfer, lead) => {
    let tags = item.tags || [];
    if (transfer && Array.isArray(transfer.tags)) {
        item.tags = _.union(tags, transfer.tags);
    } else if (lead && Array.isArray(lead.tags)) {
        item.tags = _.union(tags, lead.tags);
    }
    return item.tags;
}
const findData = async (phones, emails, leadIds, campaignKey, clientId) => {
    const [transfersFound, leadsFound] = await parallel(2, [findInTransfers, findInLeads],
        async (fn) => fn(phones, emails, campaignKey, leadIds, clientId));
    return {transfersFound, leadsFound};
};

const findInTransfers = async (phones, emails, campaignKey, leadId, clientId) => {
    const filterConditions = _.compact([
        buildFilterCondition('email', emails),
        buildFilterCondition('consumer_phone', phones),
        buildFilterCondition('lead_id', leadId)
    ]);
    const INTERNAL_CLIENT = '2c4da08245944b59b2c5c7e8b2f0ef22'
    if (_.isEmpty(filterConditions)) return null;
    const filter = {
        "$and": [
            {
                "$or": filterConditions
            },
            {
                "client_id": {
                    "$in": [
                        clientId,
                        INTERNAL_CLIENT
                    ]
                }
            }
        ]
    }
    return await getTransfers(filter);
};

const findInLeads = async (phones, emails, campaignKey, leadId, clientId) => {
    const filterConditions = _.compact([
        buildFilterCondition('email', emails),
        buildFilterCondition('phone', phones),
        buildFilterCondition('_id', leadId)
    ]);
    if (_.isEmpty(filterConditions)) return null;
    const filter = {
        $or: filterConditions,
        campaign_key: campaignKey
    };
    return await getLeads(filter);
};

const buildFilterCondition = (field, values) => {
    return values && values.length > 0 ? {[field]: {$in: values}} : {[field]: []};
};
