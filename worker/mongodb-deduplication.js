#!/usr/bin/env node

/**
 * Sistema de deduplicación distribuido usando MongoDB
 */

import {createLogger, docmqConfig} from './docmq-config.js';
import {mongoConnect} from "./mongoConnect.js";

class MongoDeduplication {
    constructor(options = {}) {
        this.mongoUri = options.mongoUri || docmqConfig.driver.options.mongo.uri;
        this.database = options.database || docmqConfig.driver.options.mongo.database;
        this.collection = options.collection || 'processed_jobs';
        this.ttl = options.ttl || 3600; // 1 hora
        this.workerId = options.workerId || 'unknown';
        this.logger = createLogger(`MongoDedup-${this.workerId}`);

        this.client = null;
        this.db = null;
        this.processedCollection = null;
    }

    /**
     * Inicializar conexión y crear índices
     */
    async initialize() {
        try {
            const {client, db} = await mongoConnect()
            this.client = client
            this.db = db
            this.processedCollection = this.db.collection(this.collection);

            // Crear índices
            await this.processedCollection.createIndex(
                {jobId: 1},
                {unique: true}
            );

            // Índice TTL para auto-eliminación
            await this.processedCollection.createIndex(
                {processedAt: 1},
                {expireAfterSeconds: this.ttl}
            );

            this.logger.info(`🗄️  MongoDB deduplication initialized: TTL=${this.ttl}s`);
            return true;
        } catch (error) {
            this.logger.error(`❌ Error initializing MongoDB dedup: ${error.message}`);
            throw error;
        }
    }



    /**
     * Intentar reclamar un job atómicamente (previene race conditions)
     */
    async tryClaimJob(jobId) {
        try {
            // Verificar si este job fue liberado anteriormente (para contar reintentos)
            const previousAttempt = await this.processedCollection.findOne(
                {jobId, status: 'failed_retry'},
                {projection: {retryCount: 1}}
            );

            const retryCount = previousAttempt ? (previousAttempt.retryCount || 0) : 0;

            // Limpiar intento anterior si existe
            if (previousAttempt) {
                await this.processedCollection.deleteOne({jobId, status: 'failed_retry'});
            }

            // Intentar insertar INMEDIATAMENTE para reclamar el job
            await this.processedCollection.insertOne({
                jobId,
                workerId: this.workerId,
                claimedAt: new Date(),
                status: 'processing',
                retryCount: retryCount + 1,
                originalClaimTime: retryCount === 0 ? new Date() : undefined
            });

            if (retryCount > 0) {
                this.logger.info(`🔒 Job ${jobId} reclamado para reintento ${retryCount + 1}`);
            } else {
                this.logger.info(`🔒 Job ${jobId} reclamado exitosamente (primer intento)`);
            }

            return {claimed: true, canProcess: true, retryCount: retryCount + 1};

        } catch (error) {
            if (error.code === 11000) { // Duplicate key error
                // Job ya fue reclamado por otro worker
                const existing = await this.processedCollection.findOne(
                    {jobId},
                    {projection: {jobId: 1, workerId: 1, status: 1, claimedAt: 1, processedAt: 1, retryCount: 1}}
                );

                this.logger.warn(`⚠️  Job ${jobId} ya reclamado por ${existing?.workerId} (status: ${existing?.status}, reintento: ${existing?.retryCount || 1})`);
                return {
                    claimed: false,
                    canProcess: false,
                    reason: 'already_claimed',
                    data: existing
                };
            }

            this.logger.error(`❌ Error claiming job ${jobId}: ${error.message}`);
            // Fail-safe: permitir procesamiento en caso de error de conexión
            return {claimed: false, canProcess: true, error: error.message};
        }
    }

    /**
     * Verificar si un job ya fue procesado (usa tryClaimJob para prevenir duplicación)
     */
    async isProcessed(jobId) {
        try {
            // Primero verificar si ya existe y está completado
            const existing = await this.processedCollection.findOne(
                {jobId, status: 'completed'},
                {projection: {jobId: 1, workerId: 1, processedAt: 1}}
            );

            if (existing) {
                this.logger.warn(`⚠️  Job ${jobId} ya completado por ${existing.workerId} en ${existing.processedAt}`);
                return {processed: true, data: existing};
            }

            // Si no está completado, intentar reclamarlo
            const claimResult = await this.tryClaimJob(jobId);

            if (claimResult.canProcess) {
                return {processed: false, claimed: claimResult.claimed};
            } else {
                return {processed: true, data: claimResult.data};
            }

        } catch (error) {
            this.logger.error(`❌ Error checking MongoDB: ${error.message}`);
            // Fail-safe: permitir procesamiento en caso de error
            return {processed: false, error: error.message};
        }
    }

    /**
     * Marcar job como completado (actualiza status de 'processing' a 'completed')
     */
    async markAsProcessed(jobId, metadata = {}) {
        try {
            // Actualizar el job que ya fue reclamado por este worker
            const result = await this.processedCollection.updateOne(
                {
                    jobId,
                    workerId: this.workerId,
                    status: 'processing'
                },
                {
                    $set: {
                        status: 'completed',
                        processedAt: new Date(),
                        ...metadata
                    }
                }
            );

            if (result.matchedCount > 0) {
                this.logger.info(`✅ Job ${jobId} marcado como completado`);
                return {success: true, completed: true};
            } else {
                // Verificar si el job existe pero con diferente worker/status
                const existing = await this.processedCollection.findOne(
                    {jobId},
                    {projection: {workerId: 1, status: 1}}
                );

                if (existing) {
                    this.logger.warn(`⚠️  Job ${jobId} no pudo ser completado - Worker: ${existing.workerId}, Status: ${existing.status}`);
                    return {success: false, reason: 'not_owned_by_this_worker', data: existing};
                } else {
                    this.logger.error(`❌ Job ${jobId} no encontrado para completar`);
                    return {success: false, reason: 'job_not_found'};
                }
            }
        } catch (error) {
            this.logger.error(`❌ Error marking job ${jobId} as completed: ${error.message}`);
            return {success: false, error: error.message};
        }
    }

    /**
     * Liberar jobs "colgados" para reprocesamiento
     */
    async cleanupStuckJobs(timeoutMinutes = 10) {
        try {
            const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);

            // Buscar jobs atascados para logging detallado
            const stuckJobs = await this.processedCollection.find({
                status: 'processing',
                claimedAt: { $lt: cutoffTime }
            }, {
                projection: { jobId: 1, workerId: 1, claimedAt: 1, retryCount: 1 }
            }).toArray();

            if (stuckJobs.length === 0) {
                return {freed: 0};
            }

            // Liberar jobs para reprocesamiento (eliminar de processed_jobs)
            const result = await this.processedCollection.deleteMany({
                status: 'processing',
                claimedAt: { $lt: cutoffTime }
            });

            // Log detallado de jobs liberados
            for (const job of stuckJobs) {
                const stuckTime = Math.round((Date.now() - new Date(job.claimedAt).getTime()) / 60000);
                const retryCount = job.retryCount || 0;
                this.logger.warn(`🔄 Job ${job.jobId} liberado para reprocesamiento - Worker: ${job.workerId}, Atascado: ${stuckTime}min, Reintento: ${retryCount + 1}`);
            }

            this.logger.info(`✅ Liberados ${result.deletedCount} jobs para reprocesamiento (>${timeoutMinutes} min)`);
            return {freed: result.deletedCount, jobs: stuckJobs};

        } catch (error) {
            this.logger.error(`❌ Error liberando jobs colgados: ${error.message}`);
            return {freed: 0, error: error.message};
        }
    }

    /**
     * Intentar adquirir lock atómico para procesamiento
     */
    async acquireProcessingLock(jobId, lockTtl = 300) {
        try {
            const lockDoc = {
                jobId: `lock:${jobId}`,
                workerId: this.workerId,
                processedAt: new Date(),
                lockExpires: new Date(Date.now() + lockTtl * 1000),
                type: 'processing_lock'
            };

            // Intentar insertar lock
            const result = await this.processedCollection.insertOne(lockDoc);

            if (result.insertedId) {
                this.logger.info(`🔒 Lock adquirido para job ${jobId}`);
                return {acquired: true, lockId: result.insertedId};
            }

            return {acquired: false};
        } catch (error) {
            if (error.code === 11000) { // Duplicate key - lock ya existe
                this.logger.warn(`⚠️  Lock ya existe para job ${jobId}`);
                return {acquired: false, duplicate: true};
            }

            this.logger.error(`❌ Error acquiring lock: ${error.message}`);
            return {acquired: false, error: error.message};
        }
    }

    /**
     * Liberar lock de procesamiento
     */
    async releaseProcessingLock(jobId) {
        try {
            const result = await this.processedCollection.deleteOne({
                jobId: `lock:${jobId}`,
                workerId: this.workerId
            });

            if (result.deletedCount > 0) {
                this.logger.info(`🔓 Lock liberado para job ${jobId}`);
                return true;
            } else {
                this.logger.warn(`⚠️  Lock no encontrado para liberar: ${jobId}`);
                return false;
            }
        } catch (error) {
            this.logger.error(`❌ Error releasing lock: ${error.message}`);
            return false;
        }
    }

    /**
     * Obtener estadísticas
     */
    async getStats() {
        try {
            const processedJobs = await this.processedCollection.countDocuments({
                type: {$ne: 'processing_lock'}
            });

            const activeLocks = await this.processedCollection.countDocuments({
                type: 'processing_lock'
            });

            const byWorker = await this.processedCollection.aggregate([
                {$match: {type: {$ne: 'processing_lock'}}},
                {$group: {_id: '$workerId', count: {$sum: 1}}}
            ]).toArray();

            return {
                processedJobs,
                activeLocks,
                byWorker
            };
        } catch (error) {
            this.logger.error(`❌ Error getting stats: ${error.message}`);
            return {error: error.message};
        }
    }

    /**
     * Limpiar locks expirados
     */
    async cleanupExpiredLocks() {
        try {
            const result = await this.processedCollection.deleteMany({
                type: 'processing_lock',
                lockExpires: {$lt: new Date()}
            });

            if (result.deletedCount > 0) {
                this.logger.info(`🧹 ${result.deletedCount} locks expirados eliminados`);
            }

            return result.deletedCount;
        } catch (error) {
            this.logger.error(`❌ Error cleaning expired locks: ${error.message}`);
            return 0;
        }
    }

    /**
     * Cerrar conexión
     */
    async close() {
        try {
            if (this.client) {
                await this.client.close();
                this.logger.info('🔌 Conexión MongoDB cerrada');
            }
        } catch (error) {
            this.logger.error(`❌ Error closing MongoDB: ${error.message}`);
        }
    }
}

export {MongoDeduplication};
