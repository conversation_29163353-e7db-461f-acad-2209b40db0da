#!/usr/bin/env node

/**
 * 🚨 Sistema de alertas para producción
 */

import { SimpleMonitor } from './simple-monitor.js';
import { createLogger } from './docmq-config.js';

const logger = createLogger('ProductionAlerts');

class ProductionAlerts {
    constructor() {
        this.thresholds = {
            cpu: parseFloat(process.env.CPU_THRESHOLD_PERCENT) || 50,
            memory: parseInt(process.env.MEMORY_THRESHOLD_MB) || 200,
            errorRate: parseFloat(process.env.ERROR_RATE_THRESHOLD_PERCENT) || 5,
            maxProcessingTime: parseInt(process.env.MAX_PROCESSING_TIME_MINUTES) || 5
        };
        
        this.alertCooldown = 5 * 60 * 1000; // 5 minutos entre alertas
        this.lastAlerts = new Map();
    }

    async checkAlerts() {
        try {
            // Obtener métricas recientes (última hora)
            const metrics = await SimpleMonitor.getRecentMetrics(1);
            const stats = await SimpleMonitor.getAggregatedStats(1);
            
            if (!metrics.length || !stats) {
                this.sendAlert('NO_METRICS', 'No hay métricas disponibles');
                return;
            }

            // Verificar CPU alto
            if (stats.avgCpu > this.thresholds.cpu) {
                this.sendAlert('HIGH_CPU', `CPU promedio: ${stats.avgCpu.toFixed(2)}% (límite: ${this.thresholds.cpu}%)`);
            }

            // Verificar memoria alta
            if (stats.avgMemory > this.thresholds.memory) {
                this.sendAlert('HIGH_MEMORY', `Memoria promedio: ${Math.round(stats.avgMemory)}MB (límite: ${this.thresholds.memory}MB)`);
            }

            // Verificar API health
            await this.checkAPIHealth();

            // Verificar jobs pendientes
            await this.checkPendingJobs();

        } catch (error) {
            this.sendAlert('MONITORING_ERROR', `Error en monitoreo: ${error.message}`);
        }
    }

    async checkAPIHealth() {
        try {
            const timeout = parseInt(process.env.HEALTH_CHECK_TIMEOUT_MS) || 5000;
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            const response = await fetch('http://localhost:3000/api/health', {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                this.sendAlert('API_UNHEALTHY', `API respondió con status ${response.status}`);
            }

        } catch (error) {
            this.sendAlert('API_DOWN', `API no responde: ${error.message}`);
        }
    }

    async checkPendingJobs() {
        try {
            const response = await fetch('http://localhost:3000/api/worker/stats');
            if (response.ok) {
                const stats = await response.json();
                
                // Verificar si hay muchos jobs activos
                if (stats.activeTasks > 10) {
                    this.sendAlert('HIGH_ACTIVE_JOBS', `${stats.activeTasks} jobs activos (puede indicar jobs colgados)`);
                }

                // Verificar rate de errores
                const totalJobs = stats.processedTasks + stats.errors;
                if (totalJobs > 0) {
                    const errorRate = (stats.errors / totalJobs) * 100;
                    if (errorRate > this.thresholds.errorRate) {
                        this.sendAlert('HIGH_ERROR_RATE', `Rate de errores: ${errorRate.toFixed(2)}% (límite: ${this.thresholds.errorRate}%)`);
                    }
                }
            }
        } catch (error) {
            // Error silencioso - ya se detectará en checkAPIHealth
        }
    }

    sendAlert(type, message) {
        const now = Date.now();
        const lastAlert = this.lastAlerts.get(type);

        // Cooldown para evitar spam
        if (lastAlert && (now - lastAlert) < this.alertCooldown) {
            return;
        }

        this.lastAlerts.set(type, now);

        // Log estructurado para sistemas de alertas
        const alert = {
            timestamp: new Date().toISOString(),
            level: 'ALERT',
            type: type,
            message: message,
            service: 'docmq-worker',
            environment: process.env.NODE_ENV || 'production'
        };

        // Log en formato JSON para parsing automático
        console.log(JSON.stringify(alert));

        // Aquí puedes agregar integración con:
        // - Slack/Discord webhooks
        // - PagerDuty
        // - Email notifications
        // - Monitoring services (DataDog, New Relic, etc.)
        
        this.notifyExternalSystems(alert);
    }

    async notifyExternalSystems(alert) {
        // Ejemplo: Webhook a Slack/Discord
        const webhookUrl = process.env.ALERT_WEBHOOK_URL;
        if (webhookUrl) {
            try {
                await fetch(webhookUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        text: `🚨 ${alert.type}: ${alert.message}`,
                        username: 'DocMQ Worker',
                        icon_emoji: ':warning:'
                    })
                });
            } catch (error) {
                // Error silencioso para no crear loops
            }
        }

        // Ejemplo: Métricas a servicio externo
        const metricsUrl = process.env.METRICS_ENDPOINT;
        if (metricsUrl) {
            try {
                await fetch(metricsUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(alert)
                });
            } catch (error) {
                // Error silencioso
            }
        }
    }

    async start() {
        logger.info('🚨 Iniciando sistema de alertas de producción...');
        
        // Verificar alertas cada 2 minutos
        setInterval(() => {
            this.checkAlerts();
        }, 2 * 60 * 1000);

        // Primera verificación inmediata
        await this.checkAlerts();
    }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    const alerts = new ProductionAlerts();
    
    process.on('SIGINT', () => {
        logger.info('🛑 Deteniendo sistema de alertas...');
        process.exit(0);
    });
    
    alerts.start();
}

export { ProductionAlerts };
