import {Hono} from "hono";
import {cors} from "hono/cors";
import {serve} from "@hono/node-server";
import os from 'os';

export class HttpServer {
    constructor(worker = null) {
        this.app = new Hono().basePath("/api");
        this.port = process.env.PORT || 3000;
        this.isRunning = false;
        this.server = null;
        this.worker = worker; // Referencia al worker para acceder a estadísticas

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // CORS para permitir requests desde frontend
        this.app.use("/*", cors({
            origin: "*",
            allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allowHeaders: ["Content-Type", "Authorization"],
        }));

    }

    setupRoutes() {
        // Health check básico
        this.app.get("/health", (c) => {
            return c.json({
                status: "ok",
                service: "stream-docmq-worker",
                version: process.env.VERSION || "1.0.0",
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || "development"
            });
        });

        // Información del worker
        this.app.get("/worker/info", (c) => {
            const workerInfo = {
                instanceId: this.worker?.instanceId || "unknown",
                concurrency: this.worker?.concurrency || 0,
                isRunning: this.worker?.isRunning || false,
                serverInfo: {
                    hostname: os.hostname(),
                    platform: os.platform(),
                    cpus: os.cpus().length,
                    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024), // GB
                    freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024),   // GB
                    loadAverage: os.loadavg(),
                    nodeVersion: process.version,
                    pid: process.pid
                }
            };

            return c.json(workerInfo);
        });

        // Estadísticas del worker desde base de datos
        this.app.get("/worker/stats", async (c) => {
            if (!this.worker || !this.worker.metrics) {
                return c.json({error: "Worker not initialized"}, 503);
            }

            try {
                const stats = await this.worker.metrics.getWorkerStats();
                return c.json(stats);
            } catch (error) {
                return c.json({error: "Failed to get stats", message: error.message}, 500);
            }
        });

        // Estado del worker desde base de datos
        this.app.get("/worker/status", async (c) => {
            if (!this.worker || !this.worker.metrics) {
                return c.json({error: "Worker not initialized"}, 503);
            }

            try {
                const stats = await this.worker.metrics.getWorkerStats();

                const status = {
                    worker: {
                        instanceId: this.worker.instanceId,
                        isRunning: this.worker.isRunning,
                        concurrency: this.worker.concurrency,
                        utilization: stats.utilization
                    },
                    stats: {
                        processedTasks: stats.processedTasks,
                        skippedTasks: stats.skippedTasks,
                        errors: stats.errors,
                        activeTasks: stats.activeTasks,
                        uptime: stats.uptime,
                        jobsPerMinute: stats.jobsPerMinute,
                        avgProcessingTime: stats.avgProcessingTime
                    }
                };

                return c.json(status);
            } catch (error) {
                return c.json({error: "Failed to get status", message: error.message}, 500);
            }
        });

        // Control del worker
        this.app.post("/worker/pause", async (c) => {
            if (!this.worker) {
                return c.json({error: "Worker not initialized"}, 503);
            }

            // Aquí podrías implementar lógica de pausa
            return c.json({
                message: "Pause functionality not implemented yet",
                instanceId: this.worker.instanceId
            });
        });

        this.app.post("/worker/resume", async (c) => {
            if (!this.worker) {
                return c.json({error: "Worker not initialized"}, 503);
            }

            // Aquí podrías implementar lógica de resume
            return c.json({
                message: "Resume functionality not implemented yet",
                instanceId: this.worker.instanceId
            });
        });

        // Métricas para Prometheus/Grafana desde base de datos
        this.app.get("/metrics", async (c) => {
            if (!this.worker || !this.worker.metrics) {
                return c.text("# Worker not initialized\n");
            }

            try {
                const stats = await this.worker.metrics.getWorkerStats();
                const utilization = stats.utilization / 100; // Convert percentage to decimal

                const metrics = `# HELP worker_processed_tasks_total Total number of processed tasks
# TYPE worker_processed_tasks_total counter
worker_processed_tasks_total{instance_id="${this.worker.instanceId}"} ${stats.processedTasks}

# HELP worker_active_tasks Current number of active tasks
# TYPE worker_active_tasks gauge
worker_active_tasks{instance_id="${this.worker.instanceId}"} ${stats.activeTasks}

# HELP worker_utilization Worker utilization percentage
# TYPE worker_utilization gauge
worker_utilization{instance_id="${this.worker.instanceId}"} ${utilization}
`;

                return c.text(metrics);
            } catch (error) {
                return c.text(`# Error getting metrics: ${error.message}\n`);
            }
        });

        // Métricas detalladas con historial
        this.app.get("/worker/metrics/detailed", async (c) => {
            if (!this.worker || !this.worker.metrics) {
                return c.json({error: "Worker not initialized"}, 503);
            }

            try {
                const hours = parseInt(c.req.query('hours')) || 1;
                const detailedMetrics = await this.worker.metrics.getDetailedMetrics(hours);
                return c.json(detailedMetrics);
            } catch (error) {
                return c.json({error: "Failed to get detailed metrics", message: error.message}, 500);
            }
        });

        // Lista de endpoints disponibles
        this.app.get("/", (c) => {
            const endpoints = {
                service: "stream-docmq-worker",
                version: process.env.VERSION || "1.0.0",
                endpoints: {
                    health: "GET /api/health",
                    workerInfo: "GET /api/worker/info",
                    workerStats: "GET /api/worker/stats",
                    workerStatus: "GET /api/worker/status",
                    workerPause: "POST /api/worker/pause",
                    workerResume: "POST /api/worker/resume",
                    metrics: "GET /api/metrics",
                    detailedMetrics: "GET /api/worker/metrics/detailed?hours=1"
                }
            };

            return c.json(endpoints);
        });
    }


    async start() {
        if (this.isRunning) {
            console.warn("⚠️  HTTP server is already running");
            return;
        }

        try {
            this.isRunning = true;

            // Iniciar servidor HTTP usando @hono/node-server
            this.server = serve({
                fetch: this.app.fetch,
                port: this.port,
            });

            console.log(`🌐 Worker API server started at http://localhost:${this.port}/api`);
            console.log(`📊 Health check: http://localhost:${this.port}/api/health`);
            console.log(`📈 Worker stats: http://localhost:${this.port}/api/worker/stats`);
            console.log(`📋 All endpoints: http://localhost:${this.port}/api`);

            return {
                port: this.port,
                fetch: this.app.fetch,
                server: this.server
            };
        } catch (error) {
            console.error("❌ Failed to start HTTP server:", error);
            this.isRunning = false;
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            console.warn("⚠️  HTTP server is not running");
            return;
        }

        try {
            if (this.server) {
                this.server.close();
                console.log("🛑 HTTP server stopped");
            }
            this.isRunning = false;
        } catch (error) {
            console.error("❌ Error stopping HTTP server:", error);
            throw error;
        }
    }

    // Método para actualizar la referencia del worker
    setWorker(worker) {
        this.worker = worker;
    }

    getServerConfig() {
        return {
            port: this.port,
            fetch: this.app.fetch,
        };
    }
}

export default HttpServer
