# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=conversion_finder
COLLECTION_QUEUES_NAME=file_processing_queue
PROCESSING_TASKS_COLLECTION=file_processing_tasks
DATABROKER_QUEUES_COLLECTION=databroker_queues

# Worker Configuration
WORKER_COUNT=2
LOG_LEVEL=info

# DocMQ Configuration (Optimizado para tareas de 5-7 min)
DOCMQ_VISIBILITY_TIMEOUT=600
DOCMQ_POLL_INTERVAL=2000

# Cleanup Configuration
CLEANUP_STUCK_JOBS_TIMEOUT=10
CLEANUP_INTERVAL=600000
ORPHAN_CHECK_INTERVAL=1800000

# Performance Tuning
# Reduce these values if experiencing high CPU/Memory usage
WORKER_POLL_INTERVAL=1000
WORKER_VISIBILITY_TIMEOUT=5
WORKER_STATS_INTERVAL=60000
WORKER_HEARTBEAT_INTERVAL=30000
WORKER_CLEANUP_INTERVAL=600000

# Memory Management
NODE_OPTIONS=--max-old-space-size=512 --gc-interval=100 --expose-gc
UV_THREADPOOL_SIZE=4

# MongoDB Connection Pool Settings
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=2
MONGODB_MAX_IDLE_TIME=30000
MONGODB_SERVER_SELECTION_TIMEOUT=5000
MONGODB_SOCKET_TIMEOUT=45000

# Cache Settings
DEDUP_CACHE_MAX_SIZE=1000
DEDUP_CACHE_TTL=3600
DEDUP_CACHE_CLEANUP_INTERVAL=300000
