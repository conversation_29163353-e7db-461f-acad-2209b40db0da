import {Queue} from 'docmq';
import {createDriver, createLogger, docmqConfig, getFileProcessingQueueConfig} from './docmq-config.js';
import {MongoDeduplication} from './mongodb-deduplication.js';
import {DatabaseMetrics} from './database-metrics.js';
import os from 'os';
import {HttpServer} from "./server.js";
import {mongoConnect} from "./mongoConnect.js";
import {DateTime} from 'luxon';
import {taskFactory} from "./tasks/taskFactory.js";
import {tryit} from "radash";
import {updateOne} from "./mongo/updateOne.js";
import {Collections} from "./mongo/collections.enum.js";
import {TaskErrorsEnum} from "./functions/utils/task-errors.enum.js";
import {createSlackClient} from "@stream-docmq/lib/slack/index.js";

class LoadBalancedWorker {
    constructor(options = {}) {
        const hostname = os.hostname();
        this.instanceId = `lb-worker-${hostname}`;
        this.concurrency = docmqConfig.queues.file_processing_tasks.processor.concurrency
        this.serverInfo = options.serverInfo || {};
        this.processingTasksCollection = process.env.PROCESSING_TASKS_COLLECTION || 'file_processing_tasks';
        this.databrokerCollection = process.env.DATABROKER_QUEUES_COLLECTION || 'databroker_queues';
        this.logger = createLogger(`LBWorker-${this.instanceId}`);
        this.metrics = null;
        this.deduplication = new MongoDeduplication({
            workerId: this.instanceId,
            ttl: 3600
        });

        this.mongoClient = null;

        this.driver = null;
        this.queue = null;
        this.isRunning = false;

        this.heartbeatTimer = null;
        this.cleanupTimer = null;

        this.httpServer = new HttpServer(this);
        this.apiPort = options.apiPort || process.env.PORT || 3000;

        this.client = createSlackClient({
            token: '*******************************************************',
            defaultChannel: '#task-worker',
            loggerService: 'worker-service'
        });
    }

    async initialize() {
        try {
            this.logger.info(`🚀 Inicializando worker load-balanced`);

            await this.initializeMongoDB();
            await this.initializeMetrics();
            await this.initializeDeduplication();
            await this.initializeDocMQQueue();

            this.logger.success(`✅ Worker load-balanced inicializado`);
            return true;
        } catch (error) {
            this.logger.error(`❌ Error inicializando worker: ${error.message}`);
            throw error;
        }
    }

    async initializeMongoDB() {
        const {client, db} = await mongoConnect();
        this.db = db;
        this.mongoClient = client;
        this.logger.info(`🔌 MongoDB conectado`);
    }

    async initializeMetrics() {
        this.metrics = new DatabaseMetrics({
            instanceId: this.instanceId,
            db: this.db,
            concurrency: this.concurrency
        });
        await this.metrics.initialize();
        this.logger.info(`📊 Métricas inicializadas`);
    }

    async initializeDeduplication() {
        await this.deduplication.initialize();
        this.logger.info(`🔒 Deduplicación inicializada`);
    }

    async initializeDocMQQueue() {
        this.driver = await createDriver();
        const queueConfig = getFileProcessingQueueConfig();
        this.queue = new Queue(this.driver, queueConfig.name, queueConfig.options);
        this.logger.info(`📦 Cola DocMQ inicializada`);
    }

    async registerWithLoadBalancer() {
        if (this.metrics) {
            await this.metrics.registerWithLoadBalancer();
        }
    }

    async processTask(job, api) {
        const jobId = api.ref;
        const startTime = Date.now();

        await this.metrics.recordJobStart();

        try {
            const skipResult = await this.checkJobStatus(job, api, jobId);
            if (skipResult) return skipResult;
            const deduplicationResult = await this.checkDeduplication(api, jobId);
            if (deduplicationResult) return deduplicationResult;
            const result = await this.executeTask(job, jobId);
            await this.markJobAsCompleted(job, jobId);
            await this.updateTraceCounters(job, result);
            return await this.finalizeJobProcessing(api, jobId, startTime);

        } catch (error) {
            await this.handleJobError(error, api, jobId);
            await this.client.sendMessage(error?.message, {
                username: 'Worker Bot',
                iconEmoji: ':robot_face:'
            })
            throw error;
        }
    }

    async checkJobStatus(job, api, jobId) {
        const atomicUpdate = await this.db.collection(this.processingTasksCollection).updateOne(
            {
                _id: job._id,
                status: {$nin: ['processing', 'processed']}
            },
            {
                $set: {
                    status: 'processing',
                    processing_at: DateTime.now().toISO()
                },
                $addToSet: {processed_by: this.instanceId}
            }
        );

        if (atomicUpdate.matchedCount === 0) {
            const skipReason = 'Already being processed by another worker';
            await this.metrics.recordJobSkipped(skipReason);
            await api.ack({
                success: true,
                skipped: true,
                reason: skipReason,
                workerId: this.instanceId
            });
            return {success: true, skipped: true};
        }
        return null;
    }

    async checkDeduplication(api, jobId) {
        const {processed, claimed} = await this.deduplication.isProcessed(jobId);

        if (processed) {
            const skipReason = 'Already processed or claimed by another worker';
            await this.metrics.recordJobSkipped(skipReason);
            await api.ack({
                success: true,
                skipped: true,
                reason: skipReason,
                workerId: this.instanceId
            });
            return {success: true, skipped: true};
        }

        if (claimed) {
            this.logger.info(`🔒 Job ${jobId} reclamado - iniciando procesamiento`);
        }
        return null;
    }

    async executeTask(job, jobId) {
        const currentStats = await this.metrics.getWorkerStats();
        this.logger.info(`🔄 [${currentStats.activeTasks}/${this.concurrency}] Procesando: ${jobId}`);
        const task = job.originalDocument.task;
        const [err, runTask] = await tryit(taskFactory)(task);
        if (err) {
            this.logger.error("Error to get function task: ", err)
            const result = await updateOne(Collections.DatabrokerQueues, {
                _id: job.originalDocument.trace_id,
                status: {$ne: 'failed'}
            }, {
                $set: {
                    status: 'failed',
                    failed_at: new Date(),
                    failure_reason: err.message
                }
            });
            console.log('resultresultresultresultresultresult: ', result)
            // await this.client.sendMessage("Error to get function task", {
            //     username: 'Worker Bot',
            // })
            return;
        }
        const [errorTask, resultTask] = await tryit(runTask)(job.originalDocument);
        if (errorTask) {
            if (errorTask.cause && errorTask.cause === TaskErrorsEnum.MAPPING_CONFIGURATION_ERROR) {
                const resultUpdate = await updateOne(Collections.DatabrokerQueues, {
                    _id: job.originalDocument.trace_id,
                    status: {$ne: 'failed'}
                }, {
                    $set: {
                        status: 'failed',
                        failed_at: new Date(),
                        failure_reason: errorTask.message
                    }
                });
                if (resultUpdate.modifiedCount > 0) {
                    await this.client.sendMessage(errorTask?.message, {
                        username: 'Worker Bot',
                    })
                }
                return;
            } else {
                await this.client.sendMessage(errorTask?.message, {
                    username: 'Worker Bot',
                })
                throw errorTask;
            }
        }
        return resultTask?.result;
    }

    async markJobAsCompleted(job, jobId) {
        await this.db.collection(this.processingTasksCollection).updateOne(
            {_id: job._id},
            {$set: {status: 'processed'}, $addToSet: {processed_by: this.instanceId}}
        );
        await this.deduplication.markAsProcessed(jobId, {
            instanceId: this.instanceId
        });
    }

    async updateTraceCounters(job, result) {
        if (!job.originalDocument?.trace_id) return;

        const traceId = job.originalDocument.trace_id;
        const databrokerCollection = this.databrokerCollection
        const totalUpsertedCount = result?.totalUpsertedCount ?? 0;
        const totalModifiedCount = result?.totalModifiedCount ?? 0;

        try {
            await this.db.collection(databrokerCollection).updateOne(
                {_id: traceId},
                {
                    $inc: {
                        completedJobs: 1,
                        totalUpsertedCount: totalUpsertedCount,
                        totalModifiedCount: totalModifiedCount
                    }
                }
            );
            this.logger.info(`📊 Contador incrementado para trace: ${traceId}`);
        } catch (error) {
            this.logger.error(`❌ Error incrementando contador trace ${traceId}: ${error.message}`);
        }
    }

    async finalizeJobProcessing(api, jobId, startTime) {
        const processingTime = Date.now() - startTime;
        await this.metrics.recordJobComplete(processingTime);

        const resultData = {
            success: true,
            processedAt: new Date().toISOString(),
            workerId: this.instanceId,
            processingTime,
            jobRef: jobId
        };

        const updatedStats = await this.metrics.getWorkerStats();
        this.logger.success(`✅ Job ${jobId} completado en ${processingTime}ms - Total: ${updatedStats.processedTasks}`);

        await api.ack(resultData);
        return resultData;
    }

    async handleJobError(error, api, jobId) {
        await this.metrics.recordJobError(error);
        this.logger.error(`❌ Error procesando ${jobId}: ${error.message}`);
        await api.fail(error);
    }


    async start() {
        try {
            await this.initialize();
            this.isRunning = true;

            await this.startHttpServer();
            this.showWorkerInfo();
            await this.registerWithLoadBalancer();
            this.startQueueProcessing();
            this.setupTimers();
            this.setupSignalHandlers();

            this.logger.success(`✅ Worker load-balanced iniciado con ${this.concurrency} concurrencia`);

        } catch (error) {
            this.logger.error('❌ Error iniciando worker:', error.message);
            process.exit(1);
        }
    }

    async startHttpServer() {
        await this.httpServer.start();
        this.logger.info(`🚀 Servidor HTTP iniciado`);
    }

    startQueueProcessing() {
        this.queue.process(async (job, api) => {
                return await this.processTask(job, api);
            },
            {
                concurrency: this.concurrency,
                visibility: parseInt(process.env.DOCMQ_VISIBILITY_TIMEOUT) || 1200,
                pollInterval: parseInt(process.env.DOCMQ_POLL_INTERVAL) || 2000
            }
        );
        this.logger.info(`📦 Procesamiento de cola iniciado`);
    }

    setupTimers() {
        this.setupHeartbeatTimer();
        this.setupCleanupTimer();
    }

    setupHeartbeatTimer() {
        this.heartbeatTimer = setInterval(async () => {
            if (this.isRunning) {
                try {
                    await this.registerWithLoadBalancer();
                } catch (error) {
                    this.logger.error(`❌ Error en heartbeat: ${error.message}`);
                }
            }
        }, 60000);
        this.logger.info(`💓 Timer de heartbeat configurado (60s)`);
    }

    setupCleanupTimer() {
        this.cleanupTimer = setInterval(async () => {
            if (this.isRunning) {
                try {
                    const cleanupResult = await this.deduplication.cleanupStuckJobs(10);
                    if (cleanupResult.freed > 0) {
                        this.logger.info(`🔄 ${cleanupResult.freed} jobs liberados para reprocesamiento`);
                    }

                    if (this.deduplication.cleanupExpiredLocks) {
                        await this.deduplication.cleanupExpiredLocks();
                    }
                } catch (error) {
                    this.logger.error(`❌ Error en cleanup: ${error.message}`);
                }
            }
        }, 10 * 60 * 1000);
        this.logger.info(`🧹 Timer de cleanup configurado (10min)`);
    }

    setupSignalHandlers() {
        process.on('SIGINT', () => this.gracefulShutdown());
        process.on('SIGTERM', () => this.gracefulShutdown());
        this.logger.info(`🔧 Manejadores de señales configurados`);
    }


    showWorkerInfo() {
        console.log('='.repeat(80));
        console.log('🌐 WORKER LOAD-BALANCED WITH API');
        console.log('='.repeat(80));
        console.log(`📋 Instance ID: ${this.instanceId}`);
        console.log(`🔄 Concurrencia: ${this.concurrency}`);
        console.log(`🖥️  Servidor: ${os.hostname()}`);
        console.log(`🖥️  CPUs: ${os.cpus().length}`);
        console.log(`💾 Memoria: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB total, ${Math.round(os.freemem() / 1024 / 1024 / 1024)}GB libre`);
        console.log(`🌐 Load Balancer: Registrado automáticamente`);
        console.log(`🚀 API Server: http://localhost:${this.apiPort}/api`);
        console.log(`📊 Health Check: http://localhost:${this.apiPort}/api/health`);
        console.log(`📈 Worker Stats: http://localhost:${this.apiPort}/api/worker/stats`);
        console.log(`⏱️  Iniciado: ${new Date().toLocaleString()}`);
        console.log(`🔧 Visibility Timeout: ${parseInt(process.env.DOCMQ_VISIBILITY_TIMEOUT) || 600}s`);
        console.log(`🔄 Poll Interval: ${parseInt(process.env.DOCMQ_POLL_INTERVAL) || 2000}ms`);
        console.log(`🕐 Verificación huérfanos: en Producer (instancia única)`);
        console.log('='.repeat(80));
        console.log('🎯 Worker registrado con Load Balancer distribuido...\n');
    }


    async gracefulShutdown() {
        this.logger.info('🛑 Iniciando cierre graceful...');
        this.isRunning = false;

        try {
            this.clearTimers();
            await this.waitForActiveTasks();
            await this.cleanupResources();
            await this.closeConnections();

            this.logger.success('✅ Worker load-balanced cerrado correctamente');
            process.exit(0);
        } catch (error) {
            this.logger.error('❌ Error en cierre graceful:', error.message);
            process.exit(1);
        }
    }

    clearTimers() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
            this.logger.info('💓 Timer de heartbeat detenido');
        }
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
            this.logger.info('🧹 Timer de cleanup detenido');
        }
    }

    async waitForActiveTasks() {
        const maxWaitTime = 30000; // 30 segundos máximo
        const startWait = Date.now();
        let currentStats = await this.metrics.getWorkerStats();

        while (currentStats.activeTasks > 0 && (Date.now() - startWait) < maxWaitTime) {
            this.logger.info(`⏳ Esperando ${currentStats.activeTasks} tareas activas...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            currentStats = await this.metrics.getWorkerStats();
        }

        if (currentStats.activeTasks > 0) {
            this.logger.warn(`⚠️  Forzando cierre con ${currentStats.activeTasks} tareas activas`);
        }
    }

    async cleanupResources() {
        if (this.metrics) {
            await this.metrics.cleanup();
            this.logger.info('📊 Métricas limpiadas');
        }
    }

    async closeConnections() {
        if (this.queue) {
            await this.queue.remove();
            this.logger.info('📦 Cola DocMQ cerrada');
        }

        if (this.deduplication) {
            await this.deduplication.close();
            this.logger.info('🔒 Deduplicación cerrada');
        }

        if (this.mongoClient) {
            await this.mongoClient.close();
            this.logger.info('🔌 Cliente MongoDB cerrado');
        }
    }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    const options = {};
    const worker = new LoadBalancedWorker(options);
    worker.start();
}

export {LoadBalancedWorker};
