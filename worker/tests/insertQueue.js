import {createSlackClient} from "@stream-docmq/lib/slack/index.js";

// await insertOne(Collections.FileProcessingTasks, {
//     "_id": generateId(),
//     "payload": {
//         "path": "imports/transfers/ecp",
//         "fileName": "ECP Xfer Duration Report_06_06-06_06 - Hour_ 23.csv_0.csv",
//         "originalFileName": "ECP Xfer Duration Report_06_06-06_06 - Hour_ 23.csv",
//         "taskName": "transfersTask",
//         "mappingConfiguration": {
//             "db_config": {
//                 "table_name": "transfers",
//                 "data_base_connection": "mongo"
//             },
//             "constants": {
//                 "utfFix": {
//                     "value": true
//                 }
//             },
//             "preConfigurationMethods": {
//                 "methods": [
//                     "removeIgnoredPhones",
//                     "assignTransferVendor",
//                     "fixTransfers",
//                     "assignClientId",
//                     "assignId"
//                 ],
//                 "args": {
//                     "staticLeadId": "outbound-generic-lead",
//                     "defaultVendorKey": "unknown",
//                     "defaultCampaignKey": "unknown"
//                 }
//             },
//             "mongo": {
//                 "configuration": "defaultTransfers"
//             },
//             "callbacks": {
//                 "error": [],
//                 "success": [
//                     {
//                         "method": "verifyMajorOperation",
//                         "configuration": {
//                             "collection": "transfers"
//                         }
//                     }
//                 ]
//             },
//             "columns": {
//                 "leadId": {
//                     "output": "lead_id",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "completedDate": {
//                     "output": "completed_date",
//                     "transformer": "date",
//                     "transformer_config": {
//                         "fromFormats": [
//                             "yyyy-MM-dd HH:mm:ss",
//                             "M/d/yyyy HH:mm",
//                             "MM/dd/yyyy HH:mm",
//                             "yyyy-MM-dd'T'HH:mm:ss.SSSZZ",
//                             "MM/dd/yy H:mm",
//                             "M/dd/yy H:mm",
//                             "yyyy-MM-dd",
//                             "yyyy-MM-dd'T'HH:mm:ss",
//                             "yyyy-MM-dd'T'HH:mm:ssZZ",
//                             "MM-dd-yy",
//                             "MM-dd-yyyy",
//                             "MM/dd/yy",
//                             "MM/dd/yyyy",
//                             "M/d/yyyy h:mm a",
//                             "M/d/yyyy"
//                         ],
//                         "sourceTimeZone": "America/New_York"
//                     }
//                 },
//                 "callId": {
//                     "output": "call_id",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "email": {
//                     "output": "email",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "agentName": {
//                     "output": "agent_name",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "duration": {
//                     "output": "duration",
//                     "transformer": "numeric",
//                     "transformer_config": ""
//                 },
//                 "transferredTo": {
//                     "output": "transferred_to",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "consumerPhone": {
//                     "output": "consumer_phone",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "subId": {
//                     "output": "sub_id",
//                     "transformer": "copy",
//                     "transformer_config": "",
//                     "default": ""
//                 },
//                 "pubId": {
//                     "output": "pub_id",
//                     "transformer": "copy",
//                     "transformer_config": "",
//                     "default": ""
//                 },
//                 "filename": {
//                     "output": "file_name",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "publisherName": {
//                     "output": "publisher_name",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "pubPlacementName": {
//                     "output": "pub_placement_name",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "pubPropertyName": {
//                     "output": "pub_property_name",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "deviceType": {
//                     "output": "device_type",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 },
//                 "age": {
//                     "output": "age",
//                     "transformer": "numeric",
//                     "transformer_config": ""
//                 },
//                 "disposition": {
//                     "output": "disposition",
//                     "transformer": "copy",
//                     "transformer_config": ""
//                 }
//             }
//         }
//     },
//     "task": "notfoundtask",
//     "status": "pending",
//     "trace_id": "b9d31be7c4e6420ebabcee1b9767daff",
//     "processed_by": [
//         "lb-worker-wa-stream-docmq-6-production-6bbf54c44c-9bch4"
//     ],
//     "processing_at": "2025-06-07T03:01:13.490+00:00"
// });

const test = async () => {
    const client = createSlackClient({
        token: '*******************************************************',
        defaultChannel: '#task-worker',
        loggerService: 'example-service'
    });

    await client.sendMessage("hello world", {
        username: 'DocMQ Bot',
        iconEmoji: ':robot_face:'
    })
}


test().then(value => console.log(value)).catch(error => console.error(error));
