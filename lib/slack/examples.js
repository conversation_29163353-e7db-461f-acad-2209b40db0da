/**
 * Ejemplos de uso del módulo de Slack
 * Este archivo muestra diferentes formas de usar las funcionalidades de Slack
 */

import { SlackClient, createSlackClient, sendQuickMessage } from './index.js';

/**
 * Ejemplo 1: Crear cliente y enviar mensaje simple
 */
export async function example1_SimpleMessage() {
    try {
        // Crear cliente de Slack
        const slackClient = new SlackClient({
            token: process.env.SLACK_BOT_TOKEN,
            defaultChannel: '#general',
            loggerService: 'example-service'
        });

        // Enviar mensaje simple
        const result = await slackClient.sendMessage(
            '¡Hola desde stream-docmq! 👋',
            {
                username: 'DocMQ Bot',
                iconEmoji: ':robot_face:'
            }
        );

        console.log('Mensaje enviado:', result.ts);
        return result;
    } catch (error) {
        console.error('Error enviando mensaje:', error.message);
        throw error;
    }
}

/**
 * Ejemplo 2: Enviar notificación de job
 */
export async function example2_JobNotification() {
    try {
        const slackClient = createSlackClient({
            token: process.env.SLACK_BOT_TOKEN,
            defaultChannel: '#jobs'
        });

        // Información del job
        const jobInfo = {
            id: 'job_1234567890_abc123',
            status: 'completed',
            type: 'document-processing',
            createdAt: '2024-01-15T10:30:00Z',
            processedAt: '2024-01-15T10:35:00Z',
            result: {
                documentsProcessed: 5,
                totalPages: 25,
                processingTime: '5m 30s'
            }
        };

        // Enviar notificación
        const result = await slackClient.sendJobNotification(jobInfo, {
            includeDetails: true,
            mentionUsers: ['U1234567890'] // ID del usuario a mencionar
        });

        console.log('Notificación de job enviada:', result.ts);
        return result;
    } catch (error) {
        console.error('Error enviando notificación de job:', error.message);
        throw error;
    }
}

/**
 * Ejemplo 3: Enviar notificación de sistema
 */
export async function example3_SystemNotification() {
    try {
        const slackClient = createSlackClient({
            token: process.env.SLACK_BOT_TOKEN,
            defaultChannel: '#alerts'
        });

        // Enviar notificación de error del sistema
        const result = await slackClient.sendSystemNotification(
            'El worker #2 ha perdido conexión con la base de datos',
            {
                level: 'error',
                service: 'worker-2',
                mentionUsers: ['U1234567890', 'U0987654321']
            }
        );

        console.log('Notificación de sistema enviada:', result.ts);
        return result;
    } catch (error) {
        console.error('Error enviando notificación de sistema:', error.message);
        throw error;
    }
}

/**
 * Ejemplo 4: Enviar mensaje con formato avanzado
 */
export async function example4_RichMessage() {
    try {
        const slackClient = createSlackClient({
            token: process.env.SLACK_BOT_TOKEN,
            defaultChannel: '#reports'
        });

        // Crear bloques para mensaje rico
        const blocks = [
            {
                type: 'header',
                text: {
                    type: 'plain_text',
                    text: '📊 Reporte Diario de Procesamiento'
                }
            },
            {
                type: 'section',
                fields: [
                    {
                        type: 'mrkdwn',
                        text: '*Jobs Completados:*\n150'
                    },
                    {
                        type: 'mrkdwn',
                        text: '*Jobs Fallidos:*\n3'
                    },
                    {
                        type: 'mrkdwn',
                        text: '*Tiempo Promedio:*\n2m 45s'
                    },
                    {
                        type: 'mrkdwn',
                        text: '*Documentos Procesados:*\n1,247'
                    }
                ]
            },
            {
                type: 'section',
                text: {
                    type: 'mrkdwn',
                    text: '*Estado del Sistema:* ✅ Operacional\n*Próxima Revisión:* Mañana a las 09:00'
                }
            },
            {
                type: 'actions',
                elements: [
                    {
                        type: 'button',
                        text: {
                            type: 'plain_text',
                            text: 'Ver Detalles'
                        },
                        url: 'https://dashboard.stream-docmq.com/reports'
                    },
                    {
                        type: 'button',
                        text: {
                            type: 'plain_text',
                            text: 'Descargar CSV'
                        },
                        url: 'https://dashboard.stream-docmq.com/export/daily.csv'
                    }
                ]
            }
        ];

        const result = await slackClient.sendRichMessage(blocks, {
            text: 'Reporte Diario de Procesamiento'
        });

        console.log('Mensaje rico enviado:', result.ts);
        return result;
    } catch (error) {
        console.error('Error enviando mensaje rico:', error.message);
        throw error;
    }
}

/**
 * Ejemplo 5: Función de conveniencia para mensaje rápido
 */
export async function example5_QuickMessage() {
    try {
        // Enviar mensaje rápido sin crear cliente
        const result = await sendQuickMessage(
            process.env.SLACK_BOT_TOKEN,
            '#general',
            '🚀 Sistema iniciado correctamente',
            {
                username: 'System Bot',
                iconEmoji: ':rocket:'
            }
        );

        console.log('Mensaje rápido enviado:', result.ts);
        return result;
    } catch (error) {
        console.error('Error enviando mensaje rápido:', error.message);
        throw error;
    }
}

/**
 * Ejemplo 6: Verificar conexión
 */
export async function example6_TestConnection() {
    try {
        const slackClient = createSlackClient({
            token: process.env.SLACK_BOT_TOKEN
        });

        const connectionInfo = await slackClient.testConnection();
        
        console.log('Conexión exitosa:', {
            user: connectionInfo.user,
            team: connectionInfo.team,
            url: connectionInfo.url
        });

        return connectionInfo;
    } catch (error) {
        console.error('Error de conexión:', error.message);
        throw error;
    }
}

/**
 * Ejemplo 7: Enviar notificación de job fallido
 */
export async function example7_FailedJobNotification() {
    try {
        const slackClient = createSlackClient({
            token: process.env.SLACK_BOT_TOKEN,
            defaultChannel: '#alerts'
        });

        const failedJobInfo = {
            id: 'job_1234567890_error',
            status: 'failed',
            type: 'document-conversion',
            createdAt: '2024-01-15T11:00:00Z',
            processedAt: '2024-01-15T11:05:00Z',
            error: 'Failed to convert PDF: Corrupted file header'
        };

        const result = await slackClient.sendJobNotification(failedJobInfo, {
            includeDetails: true,
            mentionUsers: ['U1234567890']
        });

        console.log('Notificación de job fallido enviada:', result.ts);
        return result;
    } catch (error) {
        console.error('Error enviando notificación de job fallido:', error.message);
        throw error;
    }
}

/**
 * Función para ejecutar todos los ejemplos
 * NOTA: Requiere configurar SLACK_BOT_TOKEN en las variables de entorno
 */
export async function runAllExamples() {
    console.log('🚀 Ejecutando ejemplos de Slack...\n');

    if (!process.env.SLACK_BOT_TOKEN) {
        console.error('❌ SLACK_BOT_TOKEN no está configurado en las variables de entorno');
        return;
    }

    const examples = [
        { name: 'Mensaje Simple', fn: example1_SimpleMessage },
        { name: 'Notificación de Job', fn: example2_JobNotification },
        { name: 'Notificación de Sistema', fn: example3_SystemNotification },
        { name: 'Mensaje Rico', fn: example4_RichMessage },
        { name: 'Mensaje Rápido', fn: example5_QuickMessage },
        { name: 'Test de Conexión', fn: example6_TestConnection },
        { name: 'Job Fallido', fn: example7_FailedJobNotification }
    ];

    for (const example of examples) {
        try {
            console.log(`📤 Ejecutando: ${example.name}`);
            await example.fn();
            console.log(`✅ ${example.name} - Completado\n`);
            
            // Esperar un poco entre ejemplos para no saturar Slack
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
            console.error(`❌ ${example.name} - Error:`, error.message, '\n');
        }
    }

    console.log('🎉 Ejemplos completados');
}

// Exportar todos los ejemplos
export default {
    example1_SimpleMessage,
    example2_JobNotification,
    example3_SystemNotification,
    example4_RichMessage,
    example5_QuickMessage,
    example6_TestConnection,
    example7_FailedJobNotification,
    runAllExamples
};
