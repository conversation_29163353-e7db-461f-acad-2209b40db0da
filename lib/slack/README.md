# Mó<PERSON><PERSON> de <PERSON>lack - @stream-docmq/lib

Este módulo proporciona funcionalidades completas para integrar Slack en el sistema stream-docmq, permitiendo enviar notificaciones, mensajes y alertas de manera eficiente.

## Características

- ✅ **Envío de mensajes simples y con formato avanzado**
- ✅ **Notificaciones automáticas de jobs**
- ✅ **Alertas de sistema**
- ✅ **Reintentos automáticos con backoff exponencial**
- ✅ **Logging integrado**
- ✅ **Validación de parámetros**
- ✅ **Soporte para menciones y threads**

## Instalación

El módulo ya está incluido en `@stream-docmq/lib`. Solo necesitas configurar tu token de Slack.

## Configuración

### Variables de Entorno

```bash
# Token del bot de Slack (requerido)
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token

# Canal por defecto (opcional)
SLACK_DEFAULT_CHANNEL=#general

# Nivel de logging (opcional)
LOG_LEVEL=debug
```

### Configuración del Bot de Slack

1. Ve a [api.slack.com](https://api.slack.com/apps)
2. Crea una nueva app o usa una existente
3. En "OAuth & Permissions", agrega estos scopes:
   - `chat:write`
   - `chat:write.public`
   - `chat:write.customize`
4. Instala la app en tu workspace
5. Copia el "Bot User OAuth Token"

## Uso Básico

### Importar el módulo

```javascript
import { SlackClient, createSlackClient, sendQuickMessage } from '@stream-docmq/lib/slack';
```

### Crear cliente

```javascript
const slackClient = new SlackClient({
    token: process.env.SLACK_BOT_TOKEN,
    defaultChannel: '#general',
    loggerService: 'my-service'
});
```

### Enviar mensaje simple

```javascript
await slackClient.sendMessage('¡Hola desde stream-docmq! 👋', {
    username: 'DocMQ Bot',
    iconEmoji: ':robot_face:'
});
```

## Funcionalidades Principales

### 1. Mensajes Simples

```javascript
// Mensaje básico
await slackClient.sendMessage('Mensaje de prueba');

// Mensaje con opciones
await slackClient.sendMessage('Mensaje personalizado', {
    channel: '#alerts',
    username: 'Custom Bot',
    iconEmoji: ':warning:',
    threadTs: 'timestamp-del-thread'
});
```

### 2. Notificaciones de Jobs

```javascript
const jobInfo = {
    id: 'job_1234567890_abc123',
    status: 'completed', // pending, processing, completed, failed, cancelled
    type: 'document-processing',
    createdAt: '2024-01-15T10:30:00Z',
    processedAt: '2024-01-15T10:35:00Z',
    result: {
        documentsProcessed: 5,
        totalPages: 25
    }
};

await slackClient.sendJobNotification(jobInfo, {
    includeDetails: true,
    mentionUsers: ['U1234567890']
});
```

### 3. Notificaciones de Sistema

```javascript
await slackClient.sendSystemNotification(
    'El worker #2 ha perdido conexión con la base de datos',
    {
        level: 'error', // info, warn, error, success, debug
        service: 'worker-2',
        mentionUsers: ['U1234567890']
    }
);
```

### 4. Mensajes con Formato Avanzado

```javascript
const blocks = [
    {
        type: 'header',
        text: {
            type: 'plain_text',
            text: '📊 Reporte Diario'
        }
    },
    {
        type: 'section',
        fields: [
            {
                type: 'mrkdwn',
                text: '*Jobs Completados:*\n150'
            },
            {
                type: 'mrkdwn',
                text: '*Jobs Fallidos:*\n3'
            }
        ]
    }
];

await slackClient.sendRichMessage(blocks, {
    text: 'Reporte Diario de Procesamiento'
});
```

### 5. Función de Conveniencia

```javascript
// Para mensajes rápidos sin crear cliente
await sendQuickMessage(
    process.env.SLACK_BOT_TOKEN,
    '#general',
    '🚀 Sistema iniciado correctamente'
);
```

## Configuración Avanzada

### Opciones de Reintentos

```javascript
const slackClient = new SlackClient({
    token: process.env.SLACK_BOT_TOKEN,
    retryOptions: {
        maxAttempts: 5,
        baseDelay: 2000,
        maxDelay: 30000,
        exponentialBackoff: true
    }
});
```

### Logging Personalizado

```javascript
const slackClient = new SlackClient({
    token: process.env.SLACK_BOT_TOKEN,
    loggerService: 'producer-service'
});
```

## Ejemplos Completos

Consulta el archivo `examples.js` para ver ejemplos completos de todas las funcionalidades.

```javascript
import { runAllExamples } from '@stream-docmq/lib/slack/examples';

// Ejecutar todos los ejemplos (requiere SLACK_BOT_TOKEN configurado)
await runAllExamples();
```

## Manejo de Errores

```javascript
try {
    await slackClient.sendMessage('Test message');
} catch (error) {
    if (error.data?.error === 'channel_not_found') {
        console.error('Canal no encontrado');
    } else if (error.data?.error === 'invalid_auth') {
        console.error('Token inválido');
    } else {
        console.error('Error desconocido:', error.message);
    }
}
```

## Verificar Conexión

```javascript
try {
    const info = await slackClient.testConnection();
    console.log('Conectado como:', info.user);
    console.log('Workspace:', info.team);
} catch (error) {
    console.error('Error de conexión:', error.message);
}
```

## Integración con Producer y Worker

### En el Producer

```javascript
import { createSlackClient } from '@stream-docmq/lib/slack';

const slack = createSlackClient({
    token: process.env.SLACK_BOT_TOKEN,
    defaultChannel: '#jobs'
});

// Al crear un job
await slack.sendJobNotification({
    id: jobId,
    status: 'pending',
    type: 'document-conversion'
});
```

### En el Worker

```javascript
import { createSlackClient } from '@stream-docmq/lib/slack';

const slack = createSlackClient({
    token: process.env.SLACK_BOT_TOKEN,
    defaultChannel: '#jobs'
});

// Al completar un job
await slack.sendJobNotification({
    id: jobId,
    status: 'completed',
    type: 'document-conversion',
    result: processingResult
});

// En caso de error
await slack.sendJobNotification({
    id: jobId,
    status: 'failed',
    type: 'document-conversion',
    error: error.message
});
```

## Mejores Prácticas

1. **Usa canales específicos** para diferentes tipos de notificaciones
2. **Configura reintentos** apropiados para tu caso de uso
3. **Incluye información relevante** en las notificaciones
4. **Usa menciones con moderación** para evitar spam
5. **Verifica la conexión** al inicializar la aplicación
6. **Maneja errores apropiadamente** y proporciona fallbacks

## Troubleshooting

### Error: "invalid_auth"
- Verifica que el token sea correcto
- Asegúrate de que el bot esté instalado en el workspace

### Error: "channel_not_found"
- Verifica que el canal existe
- Asegúrate de que el bot tenga acceso al canal

### Error: "rate_limited"
- El módulo maneja automáticamente los rate limits con reintentos
- Considera reducir la frecuencia de mensajes si persiste

## API Reference

Consulta el código fuente en `index.js` para la documentación completa de la API.
