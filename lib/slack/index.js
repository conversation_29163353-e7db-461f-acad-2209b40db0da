

import { WebClient } from '@slack/web-api';
import { DateTime } from 'luxon';
import { retryWithBackoff, createLogger, validateRequiredProps } from '../utils/index.js';

/**
 * Cliente de Slack con funcionalidades extendidas
 */
export class SlackClient {
    constructor(options = {}) {
        const {
            token,
            defaultChannel,
            retryOptions = {},
            loggerService = 'slack-client'
        } = options;

        if (!token) {
            throw new Error('Slack token is required');
        }

        this.client = new WebClient(token);
        this.defaultChannel = defaultChannel;
        this.logger = createLogger(loggerService);
        this.retryOptions = {
            maxAttempts: 3,
            baseDelay: 1000,
            maxDelay: 10000,
            exponentialBackoff: true,
            ...retryOptions
        };
    }

    /**
     * Enviar mensaje simple a Slack
     * @param {string} text - Texto del mensaje
     * @param {Object} options - Opciones del mensaje
     * @returns {Promise<Object>} Respuesta de Slack
     */
    async sendMessage(text, options = {}) {
        const {
            channel = this.defaultChannel,
            username,
            iconEmoji,
            iconUrl,
            threadTs,
            unfurlLinks = false,
            unfurlMedia = false
        } = options;

        if (!channel) {
            throw new Error('Channel is required (either in options or as default)');
        }

        const messagePayload = {
            channel,
            text,
            unfurl_links: unfurlLinks,
            unfurl_media: unfurlMedia
        };

        if (username) messagePayload.username = username;
        if (iconEmoji) messagePayload.icon_emoji = iconEmoji;
        if (iconUrl) messagePayload.icon_url = iconUrl;
        if (threadTs) messagePayload.thread_ts = threadTs;

        return await this.sendWithRetry(messagePayload);
    }

    /**
     * Enviar mensaje con formato avanzado (blocks)
     * @param {Array} blocks - Bloques de Slack
     * @param {Object} options - Opciones del mensaje
     * @returns {Promise<Object>} Respuesta de Slack
     */
    async sendRichMessage(blocks, options = {}) {
        const {
            channel = this.defaultChannel,
            text = 'Mensaje con formato',
            username,
            iconEmoji,
            iconUrl,
            threadTs
        } = options;

        if (!channel) {
            throw new Error('Channel is required (either in options or as default)');
        }

        if (!Array.isArray(blocks)) {
            throw new Error('Blocks must be an array');
        }

        const messagePayload = {
            channel,
            text, // Texto de fallback
            blocks
        };

        if (username) messagePayload.username = username;
        if (iconEmoji) messagePayload.icon_emoji = iconEmoji;
        if (iconUrl) messagePayload.icon_url = iconUrl;
        if (threadTs) messagePayload.thread_ts = threadTs;

        return await this.sendWithRetry(messagePayload);
    }

    /**
     * Enviar notificación de estado de job
     * @param {Object} jobInfo - Información del job
     * @param {Object} options - Opciones adicionales
     * @returns {Promise<Object>} Respuesta de Slack
     */
    async sendJobNotification(jobInfo, options = {}) {
        const validation = validateRequiredProps(jobInfo, ['id', 'status']);
        if (!validation.valid) {
            throw new Error(`Invalid job info: ${validation.message}`);
        }

        const {
            channel = this.defaultChannel,
            includeDetails = true,
            mentionUsers = [],
            threadTs
        } = options;

        const { id, status, type, createdAt, processedAt, error, result } = jobInfo;

        // Determinar color y emoji según el estado
        const statusConfig = this.getJobStatusConfig(status);

        let text = `${statusConfig.emoji} Job ${id} - ${statusConfig.label}`;

        // Agregar menciones si se especifican
        if (mentionUsers.length > 0) {
            const mentions = mentionUsers.map(user => `<@${user}>`).join(' ');
            text = `${mentions} ${text}`;
        }

        const blocks = [
            {
                type: 'section',
                text: {
                    type: 'mrkdwn',
                    text: `${statusConfig.emoji} *Job ${status.toUpperCase()}*`
                }
            },
            {
                type: 'section',
                fields: [
                    {
                        type: 'mrkdwn',
                        text: `*Job ID:*\n${id}`
                    },
                    {
                        type: 'mrkdwn',
                        text: `*Status:*\n${statusConfig.label}`
                    }
                ]
            }
        ];

        if (includeDetails) {
            const detailFields = [];

            if (type) {
                detailFields.push({
                    type: 'mrkdwn',
                    text: `*Type:*\n${type}`
                });
            }

            if (createdAt) {
                detailFields.push({
                    type: 'mrkdwn',
                    text: `*Created:*\n${DateTime.fromISO(createdAt).toFormat('dd/MM/yyyy HH:mm:ss')}`
                });
            }

            if (processedAt) {
                detailFields.push({
                    type: 'mrkdwn',
                    text: `*Processed:*\n${DateTime.fromISO(processedAt).toFormat('dd/MM/yyyy HH:mm:ss')}`
                });
            }

            if (detailFields.length > 0) {
                blocks.push({
                    type: 'section',
                    fields: detailFields
                });
            }

            // Agregar información de error si existe
            if (error && status === 'failed') {
                blocks.push({
                    type: 'section',
                    text: {
                        type: 'mrkdwn',
                        text: `*Error:*\n\`\`\`${error}\`\`\``
                    }
                });
            }

            // Agregar información de resultado si existe
            if (result && status === 'completed') {
                const resultText = typeof result === 'object'
                    ? JSON.stringify(result, null, 2)
                    : String(result);

                blocks.push({
                    type: 'section',
                    text: {
                        type: 'mrkdwn',
                        text: `*Result:*\n\`\`\`${resultText}\`\`\``
                    }
                });
            }
        }

        // Agregar divider al final
        blocks.push({
            type: 'divider'
        });

        return await this.sendRichMessage(blocks, {
            channel,
            text,
            threadTs
        });
    }

    /**
     * Enviar notificación de sistema
     * @param {string} message - Mensaje del sistema
     * @param {Object} options - Opciones adicionales
     * @returns {Promise<Object>} Respuesta de Slack
     */
    async sendSystemNotification(message, options = {}) {
        const {
            channel = this.defaultChannel,
            level = 'info',
            service = 'system',
            includeTimestamp = true,
            mentionUsers = [],
            threadTs
        } = options;

        const levelConfig = this.getSystemLevelConfig(level);

        let text = `${levelConfig.emoji} [${service.toUpperCase()}] ${message}`;

        if (mentionUsers.length > 0) {
            const mentions = mentionUsers.map(user => `<@${user}>`).join(' ');
            text = `${mentions} ${text}`;
        }

        const blocks = [
            {
                type: 'section',
                text: {
                    type: 'mrkdwn',
                    text: `${levelConfig.emoji} *${levelConfig.label}*`
                }
            },
            {
                type: 'section',
                text: {
                    type: 'mrkdwn',
                    text: `*Service:* ${service}\n*Message:* ${message}`
                }
            }
        ];

        if (includeTimestamp) {
            blocks.push({
                type: 'context',
                elements: [
                    {
                        type: 'mrkdwn',
                        text: `📅 ${DateTime.now().toFormat('dd/MM/yyyy HH:mm:ss')}`
                    }
                ]
            });
        }

        return await this.sendRichMessage(blocks, {
            channel,
            text,
            threadTs
        });
    }

    /**
     * Obtener configuración de estado de job
     * @param {string} status - Estado del job
     * @returns {Object} Configuración del estado
     */
    getJobStatusConfig(status) {
        const configs = {
            pending: { emoji: '⏳', label: 'Pending' },
            processing: { emoji: '⚙️', label: 'Processing' },
            completed: { emoji: '✅', label: 'Completed' },
            failed: { emoji: '❌', label: 'Failed' },
            cancelled: { emoji: '🚫', label: 'Cancelled' }
        };

        return configs[status] || { emoji: '❓', label: 'Unknown' };
    }

    /**
     * Obtener configuración de nivel de sistema
     * @param {string} level - Nivel del mensaje
     * @returns {Object} Configuración del nivel
     */
    getSystemLevelConfig(level) {
        const configs = {
            info: { emoji: 'ℹ️', label: 'Information' },
            warn: { emoji: '⚠️', label: 'Warning' },
            error: { emoji: '🚨', label: 'Error' },
            success: { emoji: '🎉', label: 'Success' },
            debug: { emoji: '🔍', label: 'Debug' }
        };

        return configs[level] || { emoji: 'ℹ️', label: 'Information' };
    }

    /**
     * Enviar mensaje con reintentos
     * @param {Object} messagePayload - Payload del mensaje
     * @returns {Promise<Object>} Respuesta de Slack
     */
    async sendWithRetry(messagePayload) {
        return await retryWithBackoff(
            async () => {
                this.logger.debug('Sending message to Slack', {
                    channel: messagePayload.channel,
                    hasBlocks: !!messagePayload.blocks
                });

                const result = await this.client.chat.postMessage(messagePayload);

                this.logger.info('Message sent successfully to Slack', {
                    channel: messagePayload.channel,
                    ts: result.ts
                });

                return result;
            },
            {
                ...this.retryOptions,
                onRetry: (error, attempt, delay) => {
                    this.logger.warn(`Slack message failed, retrying in ${delay}ms`, {
                        attempt,
                        error: error.message,
                        channel: messagePayload.channel
                    });
                }
            }
        );
    }

    /**
     * Verificar conexión con Slack
     * @returns {Promise<Object>} Información de la conexión
     */
    async testConnection() {
        try {
            const result = await this.client.auth.test();
            this.logger.info('Slack connection test successful', {
                user: result.user,
                team: result.team
            });
            return result;
        } catch (error) {
            this.logger.error('Slack connection test failed', { error: error.message });
            throw error;
        }
    }
}

/**
 * Crear cliente de Slack con configuración
 * @param {Object} config - Configuración del cliente
 * @returns {SlackClient} Cliente de Slack configurado
 */
export function createSlackClient(config = {}) {
    const validation = validateRequiredProps(config, ['token']);
    if (!validation.valid) {
        throw new Error(`Invalid Slack config: ${validation.message}`);
    }

    return new SlackClient(config);
}

/**
 * Función de conveniencia para enviar mensaje rápido
 * @param {string} token - Token de Slack
 * @param {string} channel - Canal de destino
 * @param {string} message - Mensaje a enviar
 * @param {Object} options - Opciones adicionales
 * @returns {Promise<Object>} Respuesta de Slack
 */
export async function sendQuickMessage(token, channel, message, options = {}) {
    const client = new SlackClient({ token });
    return await client.sendMessage(message, { channel, ...options });
}

// Exportación por defecto
export default {
    SlackClient,
    createSlackClient,
    sendQuickMessage
};
