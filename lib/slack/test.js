/**
 * Test básico del módulo de Slack
 * Este archivo verifica que las importaciones y funciones básicas funcionen correctamente
 */

import { SlackClient, createSlackClient, sendQuickMessage } from './index.js';

/**
 * Test de importaciones y creación de cliente
 */
async function testImportsAndClientCreation() {
    console.log('🧪 Testing imports and client creation...');
    
    try {
        // Test 1: Verificar que las clases se importen correctamente
        console.log('✅ SlackClient imported:', typeof SlackClient === 'function');
        console.log('✅ createSlackClient imported:', typeof createSlackClient === 'function');
        console.log('✅ sendQuickMessage imported:', typeof sendQuickMessage === 'function');
        
        // Test 2: Crear cliente sin token (debe fallar)
        try {
            new SlackClient({});
            console.log('❌ Should have failed without token');
        } catch (error) {
            console.log('✅ Correctly failed without token:', error.message);
        }
        
        // Test 3: Crear cliente con token falso (debe crear el objeto)
        const fakeClient = new SlackClient({
            token: 'xoxb-fake-token-for-testing',
            defaultChannel: '#test'
        });
        console.log('✅ Client created with fake token');
        
        // Test 4: Verificar métodos del cliente
        const methods = [
            'sendMessage',
            'sendRichMessage', 
            'sendJobNotification',
            'sendSystemNotification',
            'testConnection',
            'getJobStatusConfig',
            'getSystemLevelConfig'
        ];
        
        methods.forEach(method => {
            console.log(`✅ Method ${method} exists:`, typeof fakeClient[method] === 'function');
        });
        
        // Test 5: Verificar configuraciones de estado
        const statusConfigs = ['pending', 'processing', 'completed', 'failed', 'cancelled'];
        statusConfigs.forEach(status => {
            const config = fakeClient.getJobStatusConfig(status);
            console.log(`✅ Status config for ${status}:`, config.emoji, config.label);
        });
        
        // Test 6: Verificar configuraciones de nivel de sistema
        const levelConfigs = ['info', 'warn', 'error', 'success', 'debug'];
        levelConfigs.forEach(level => {
            const config = fakeClient.getSystemLevelConfig(level);
            console.log(`✅ Level config for ${level}:`, config.emoji, config.label);
        });
        
        console.log('🎉 All basic tests passed!\n');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        throw error;
    }
}

/**
 * Test de validaciones
 */
async function testValidations() {
    console.log('🧪 Testing validations...');
    
    try {
        const fakeClient = new SlackClient({
            token: 'xoxb-fake-token-for-testing',
            defaultChannel: '#test'
        });
        
        // Test 1: sendMessage sin canal y sin defaultChannel
        const clientWithoutDefault = new SlackClient({
            token: 'xoxb-fake-token-for-testing'
        });
        
        try {
            await clientWithoutDefault.sendMessage('test');
            console.log('❌ Should have failed without channel');
        } catch (error) {
            console.log('✅ Correctly failed without channel:', error.message);
        }
        
        // Test 2: sendJobNotification con información inválida
        try {
            await fakeClient.sendJobNotification({});
            console.log('❌ Should have failed with invalid job info');
        } catch (error) {
            console.log('✅ Correctly failed with invalid job info:', error.message);
        }
        
        // Test 3: sendRichMessage con blocks inválidos
        try {
            await fakeClient.sendRichMessage('not-an-array');
            console.log('❌ Should have failed with invalid blocks');
        } catch (error) {
            console.log('✅ Correctly failed with invalid blocks:', error.message);
        }
        
        // Test 4: createSlackClient sin token
        try {
            createSlackClient({});
            console.log('❌ Should have failed without token');
        } catch (error) {
            console.log('✅ Correctly failed without token:', error.message);
        }
        
        console.log('🎉 All validation tests passed!\n');
        
    } catch (error) {
        console.error('❌ Validation test failed:', error.message);
        throw error;
    }
}

/**
 * Test de construcción de payloads (sin envío real)
 */
async function testPayloadConstruction() {
    console.log('🧪 Testing payload construction...');
    
    try {
        const fakeClient = new SlackClient({
            token: 'xoxb-fake-token-for-testing',
            defaultChannel: '#test'
        });
        
        // Mock del método sendWithRetry para capturar payloads
        const capturedPayloads = [];
        fakeClient.sendWithRetry = async (payload) => {
            capturedPayloads.push(payload);
            return { ts: '1234567890.123456', ok: true };
        };
        
        // Test 1: Mensaje simple
        await fakeClient.sendMessage('Test message', {
            username: 'Test Bot',
            iconEmoji: ':robot_face:'
        });
        
        const simplePayload = capturedPayloads[0];
        console.log('✅ Simple message payload:', {
            hasChannel: !!simplePayload.channel,
            hasText: !!simplePayload.text,
            hasUsername: !!simplePayload.username,
            hasIconEmoji: !!simplePayload.icon_emoji
        });
        
        // Test 2: Notificación de job
        await fakeClient.sendJobNotification({
            id: 'test-job-123',
            status: 'completed',
            type: 'test-processing'
        });
        
        const jobPayload = capturedPayloads[1];
        console.log('✅ Job notification payload:', {
            hasChannel: !!jobPayload.channel,
            hasBlocks: !!jobPayload.blocks,
            blocksCount: jobPayload.blocks?.length || 0
        });
        
        // Test 3: Notificación de sistema
        await fakeClient.sendSystemNotification('Test system message', {
            level: 'info',
            service: 'test-service'
        });
        
        const systemPayload = capturedPayloads[2];
        console.log('✅ System notification payload:', {
            hasChannel: !!systemPayload.channel,
            hasBlocks: !!systemPayload.blocks,
            blocksCount: systemPayload.blocks?.length || 0
        });
        
        console.log('🎉 All payload construction tests passed!\n');
        
    } catch (error) {
        console.error('❌ Payload construction test failed:', error.message);
        throw error;
    }
}

/**
 * Ejecutar todos los tests
 */
async function runAllTests() {
    console.log('🚀 Starting Slack module tests...\n');
    
    try {
        await testImportsAndClientCreation();
        await testValidations();
        await testPayloadConstruction();
        
        console.log('🎉 All tests completed successfully!');
        console.log('\n📝 Note: These tests verify the module structure and logic.');
        console.log('   To test actual Slack integration, set SLACK_BOT_TOKEN and run examples.js');
        
    } catch (error) {
        console.error('❌ Tests failed:', error.message);
        process.exit(1);
    }
}

// Ejecutar tests si este archivo se ejecuta directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    runAllTests();
}

export {
    testImportsAndClientCreation,
    testValidations,
    testPayloadConstruction,
    runAllTests
};
