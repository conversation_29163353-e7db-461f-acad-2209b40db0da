{"name": "@stream-docmq/lib", "version": "1.0.0", "description": "Librería compartida para stream-docmq producer y worker", "type": "module", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "watch": "bun run --watch index.js", "dev": "bun run --watch index.js", "build": "echo \"No build step required for ES modules\"", "link": "npm link", "unlink": "npm unlink", "prepare": "echo \"Library ready for linking\"", "watch:verbose": "bun run --watch --verbose index.js"}, "keywords": ["stream-docmq", "shared", "library", "producer", "worker"], "author": "Stream DocMQ Team", "license": "MIT", "dependencies": {"@slack/web-api": "^7.9.2", "luxon": "^3.4.4"}, "peerDependencies": {"mongodb": "^6.0.0", "dotenv": "^16.3.1"}}