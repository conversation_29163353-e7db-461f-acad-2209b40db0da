#!/usr/bin/env node

/**
 * Script de desarrollo para la librería @stream-docmq/lib
 * Facilita el desarrollo con npm link y watch mode
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Colores para la consola
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
    log('\n' + '='.repeat(50), 'cyan');
    log(`📦 ${message}`, 'bright');
    log('='.repeat(50), 'cyan');
}

function logSuccess(message) {
    log(`✅ ${message}`, 'green');
}

function logError(message) {
    log(`❌ ${message}`, 'red');
}

function logWarning(message) {
    log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
    log(`ℹ️  ${message}`, 'blue');
}

// Verificar si npm link está configurado
function checkNpmLink() {
    logHeader('Verificando configuración de npm link');
    
    try {
        const packageJson = JSON.parse(fs.readFileSync(join(__dirname, 'package.json'), 'utf8'));
        logInfo(`Librería: ${packageJson.name}@${packageJson.version}`);
        
        // Verificar si ya está linkeada globalmente
        const globalNodeModules = process.env.NODE_PATH || '/usr/local/lib/node_modules';
        const linkPath = join(globalNodeModules, packageJson.name);
        
        if (fs.existsSync(linkPath)) {
            logSuccess('La librería ya está linkeada globalmente');
            return true;
        } else {
            logWarning('La librería no está linkeada globalmente');
            return false;
        }
    } catch (error) {
        logError(`Error verificando npm link: ${error.message}`);
        return false;
    }
}

// Configurar npm link
function setupNpmLink() {
    return new Promise((resolve, reject) => {
        logHeader('Configurando npm link');
        
        const npmLink = spawn('npm', ['link'], {
            cwd: __dirname,
            stdio: 'inherit'
        });
        
        npmLink.on('close', (code) => {
            if (code === 0) {
                logSuccess('npm link configurado exitosamente');
                resolve();
            } else {
                logError(`npm link falló con código: ${code}`);
                reject(new Error(`npm link failed with code ${code}`));
            }
        });
        
        npmLink.on('error', (error) => {
            logError(`Error ejecutando npm link: ${error.message}`);
            reject(error);
        });
    });
}

// Iniciar modo watch
function startWatchMode() {
    logHeader('Iniciando modo watch');
    logInfo('Presiona Ctrl+C para detener el watch mode');
    logInfo('Los cambios se reflejarán automáticamente en los proyectos que usen npm link');
    
    const watchProcess = spawn('bun', ['run', '--watch', 'index.js'], {
        cwd: __dirname,
        stdio: 'inherit'
    });
    
    watchProcess.on('close', (code) => {
        if (code !== 0) {
            logError(`Watch mode terminó con código: ${code}`);
        } else {
            logInfo('Watch mode terminado');
        }
    });
    
    watchProcess.on('error', (error) => {
        logError(`Error en watch mode: ${error.message}`);
    });
    
    // Manejar señales de terminación
    process.on('SIGINT', () => {
        log('\n🛑 Deteniendo watch mode...', 'yellow');
        watchProcess.kill('SIGINT');
        process.exit(0);
    });
    
    process.on('SIGTERM', () => {
        log('\n🛑 Deteniendo watch mode...', 'yellow');
        watchProcess.kill('SIGTERM');
        process.exit(0);
    });
}

// Mostrar instrucciones de uso
function showUsageInstructions() {
    logHeader('Instrucciones de uso');
    
    log('Para usar esta librería en tus proyectos:', 'bright');
    log('');
    log('1. En el directorio del producer:', 'cyan');
    log('   cd ../producer && npm link @stream-docmq/lib', 'blue');
    log('');
    log('2. En el directorio del worker:', 'cyan');
    log('   cd ../worker && npm link @stream-docmq/lib', 'blue');
    log('');
    log('3. En el directorio del load-balancer:', 'cyan');
    log('   cd ../load-balancer && npm link @stream-docmq/lib', 'blue');
    log('');
    log('Los cambios en la librería se reflejarán automáticamente!', 'green');
    log('');
    log('Para deshacer el link:', 'yellow');
    log('   npm unlink @stream-docmq/lib (en cada proyecto)', 'yellow');
    log('   npm unlink (en este directorio)', 'yellow');
}

// Función principal
async function main() {
    try {
        logHeader('Stream-DocMQ Library Development Setup');
        
        // Verificar si ya está linkeado
        const isLinked = checkNpmLink();
        
        if (!isLinked) {
            logInfo('Configurando npm link...');
            await setupNpmLink();
        }
        
        // Mostrar instrucciones
        showUsageInstructions();
        
        // Iniciar watch mode
        startWatchMode();
        
    } catch (error) {
        logError(`Error en el setup: ${error.message}`);
        process.exit(1);
    }
}

// Ejecutar si es llamado directamente
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { main, checkNpmLink, setupNpmLink, startWatchMode };
