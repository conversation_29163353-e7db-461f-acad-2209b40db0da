name: develop
on:
  push:
    branches:
      - main

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - name: Create .npmrc file
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.AUTH_TOKEN_GITHUB }}" > .npmrc

      - run: npm install
      - run: npm run build
      - run: npm publish
