{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "strict": true, "noImplicitAny": false, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": true, "exactOptionalPropertyTypes": false, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "types": ["node"]}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.js"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}