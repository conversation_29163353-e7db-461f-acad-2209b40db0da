# @stream-docmq/lib

Librería compartida para stream-docmq producer y worker.

## 📦 Instalación

```bash
# Desde el directorio del producer o worker
npm install ../lib
```

## 🚀 Uso Básico

### Hello World

```javascript
import { helloWorld } from '@stream-docmq/lib';

// Uso básico
const result = helloWorld('producer');
console.log(result);

// Con opciones
const result = helloWorld('worker', {
    includeTimestamp: true,
    includeConfig: true,
    customMessage: '¡Hola desde mi servicio!'
});
```

### Configuración

```javascript
import config from '@stream-docmq/lib/config';

// Obtener configuración
const dbConfig = config.get('database');
const timeout = config.get('timeouts.jobProcessing');

// Establecer configuración
config.set('logging.level', 'debug');

// Validar configuración
const validation = config.validate();
if (!validation.valid) {
    console.error('Config errors:', validation.errors);
}
```

### Utilidades

```javascript
import { 
    generateId, 
    formatDuration, 
    formatBytes,
    retryWithBackoff,
    createLogger 
} from '@stream-docmq/lib/utils';

// Generar ID único
const jobId = generateId('job');

// Formatear duración
const duration = formatDuration(125000); // "2m 5s"

// Logger
const logger = createLogger('my-service');
logger.info('Proceso iniciado', { jobId });

// Retry con backoff
const result = await retryWithBackoff(
    () => riskyOperation(),
    { maxAttempts: 3, baseDelay: 1000 }
);
```

### Base de Datos

```javascript
import {
    QueryFilters,
    DatabaseOperations,
    createDatabaseOperations
} from '@stream-docmq/lib/database';

// Crear operaciones de DB
const dbOps = createDatabaseOperations(db);

// Buscar jobs orfanos
const filter = QueryFilters.orphanedJobs(['pending', 'processing'], 15);
const orphanedJobs = await dbOps.findJobs(filter);

// Obtener estadísticas
const stats = await dbOps.getJobStats();

// Limpiar jobs antiguos
const cleanup = await dbOps.cleanupOldJobs(7, ['processed']);
```

### Slack

```javascript
import {
    SlackClient,
    createSlackClient,
    sendQuickMessage
} from '@stream-docmq/lib/slack';

// Crear cliente de Slack
const slack = createSlackClient({
    token: process.env.SLACK_BOT_TOKEN,
    defaultChannel: '#general'
});

// Enviar mensaje simple
await slack.sendMessage('¡Hola desde stream-docmq! 👋');

// Notificación de job
await slack.sendJobNotification({
    id: 'job_123',
    status: 'completed',
    type: 'document-processing'
});

// Notificación de sistema
await slack.sendSystemNotification(
    'Worker iniciado correctamente',
    { level: 'info', service: 'worker-1' }
);

// Mensaje rápido
await sendQuickMessage(
    process.env.SLACK_BOT_TOKEN,
    '#alerts',
    '🚨 Error crítico detectado'
);
```

## 📋 API Reference

### Core Functions

#### `helloWorld(service, options)`
- **service**: Nombre del servicio que llama
- **options**: Objeto con opciones
  - `includeTimestamp`: Incluir timestamp (default: true)
  - `includeConfig`: Incluir información de config (default: false)
  - `customMessage`: Mensaje personalizado

#### `getLibraryInfo()`
Retorna información de la librería.

### Configuration

#### `config.get(key)`
Obtiene valor de configuración usando notación de punto.

#### `config.set(key, value)`
Establece valor de configuración.

#### `config.validate()`
Valida la configuración actual.

### Utils

#### `generateId(prefix)`
Genera ID único con prefijo opcional.

#### `formatDuration(ms)`
Formatea milisegundos a formato legible.

#### `formatBytes(bytes, decimals)`
Formatea bytes a formato legible.

#### `retryWithBackoff(fn, options)`
Ejecuta función con reintentos y backoff exponencial.

#### `createLogger(service)`
Crea logger con formato consistente.

### Database

#### `QueryFilters`
- `byStatus(statuses)`: Filtro por estatus
- `byProcessingTimeout(minutes)`: Filtro por timeout
- `orphanedJobs(statuses, timeout)`: Filtro para jobs orfanos
- `byDateRange(start, end, field)`: Filtro por rango de fechas

#### `DatabaseOperations`
- `findJobs(filter, options)`: Buscar jobs
- `countJobs(filter)`: Contar jobs
- `updateJobAtomic(filter, update)`: Actualización atómica
- `requeueJob(job, cleanData)`: Reencolar job
- `getJobStats(filter)`: Obtener estadísticas
- `cleanupOldJobs(days, statuses)`: Limpiar jobs antiguos

### Slack

#### `SlackClient`
- `sendMessage(text, options)`: Enviar mensaje simple
- `sendRichMessage(blocks, options)`: Enviar mensaje con formato avanzado
- `sendJobNotification(jobInfo, options)`: Notificación de job
- `sendSystemNotification(message, options)`: Notificación de sistema
- `testConnection()`: Verificar conexión con Slack

#### `createSlackClient(config)`
Crea cliente de Slack con configuración.

#### `sendQuickMessage(token, channel, message, options)`
Función de conveniencia para enviar mensaje rápido.

## 🔧 Configuración por Defecto

```javascript
{
  database: {
    defaultTimeout: 30000,
    retryAttempts: 3,
    collections: {
      processingTasks: 'file_processing_tasks',
      docmqJobs: 'docmq_jobs',
      databrokerQueues: 'databroker_queues'
    }
  },
  timeouts: {
    jobProcessing: 900000,    // 15 min
    connectionTimeout: 30000,  // 30 sec
    gracefulShutdown: 30000   // 30 sec
  },
  retry: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    exponentialBackoff: true
  }
}
```

## 🌍 Variables de Ambiente

- `LOG_LEVEL`: Nivel de logging (debug, info, warn, error)
- `NODE_ENV`: Ambiente (development, production)
- `PROCESSING_TASKS_COLLECTION`: Nombre de colección de tasks
- `COLLECTION_QUEUES_NAME`: Nombre de colección de DocMQ
- `DATABROKER_QUEUES_COLLECTION`: Nombre de colección de databroker
- `SLACK_BOT_TOKEN`: Token del bot de Slack (para módulo de Slack)
- `SLACK_DEFAULT_CHANNEL`: Canal por defecto de Slack

## 📝 Ejemplos

Ver ejemplos completos en los directorios `producer/` y `worker/` del proyecto.

## 🤝 Contribuir

1. Agregar nuevas funcionalidades en los módulos correspondientes
2. Mantener compatibilidad con producer y worker
3. Documentar nuevas funciones
4. Agregar tests si es necesario

## 📄 Licencia

MIT
