/**
 * Utilidades compartidas para stream-docmq
 */

import { DateTime } from 'luxon';

/**
 * Generar un ID único
 * @param {string} prefix - Prefijo para el ID
 * @returns {string} ID único
 */
export function generateId(prefix = 'id') {
    const timestamp = DateTime.now().toMillis();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`;
}

/**
 * Formatear duración en milisegundos a formato legible
 * @param {number} milliseconds - Duración en milisegundos
 * @returns {string} Duración formateada
 */
export function formatDuration(milliseconds) {
    if (milliseconds < 1000) {
        return `${milliseconds}ms`;
    }
    
    const seconds = Math.floor(milliseconds / 1000);
    if (seconds < 60) {
        return `${seconds}s`;
    }
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes < 60) {
        return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    return `${hours}h ${remainingMinutes}m`;
}

/**
 * Formatear bytes a formato legible
 * @param {number} bytes - Cantidad de bytes
 * @param {number} decimals - Número de decimales
 * @returns {string} Tamaño formateado
 */
export function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Crear un delay/sleep asíncrono
 * @param {number} ms - Milisegundos a esperar
 * @returns {Promise} Promise que se resuelve después del delay
 */
export function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Reintentar una función con backoff exponencial
 * @param {Function} fn - Función a reintentar
 * @param {Object} options - Opciones de reintento
 * @returns {Promise} Resultado de la función
 */
export async function retryWithBackoff(fn, options = {}) {
    const {
        maxAttempts = 3,
        baseDelay = 1000,
        maxDelay = 10000,
        exponentialBackoff = true,
        onRetry = null
    } = options;

    let lastError;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        } catch (error) {
            lastError = error;
            
            if (attempt === maxAttempts) {
                throw error;
            }
            
            const delay = exponentialBackoff 
                ? Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay)
                : baseDelay;
            
            if (onRetry) {
                onRetry(error, attempt, delay);
            }
            
            await sleep(delay);
        }
    }
    
    throw lastError;
}

/**
 * Validar si un objeto tiene las propiedades requeridas
 * @param {Object} obj - Objeto a validar
 * @param {Array} requiredProps - Propiedades requeridas
 * @returns {Object} Resultado de validación
 */
export function validateRequiredProps(obj, requiredProps) {
    const missing = requiredProps.filter(prop => {
        return obj[prop] === undefined || obj[prop] === null;
    });
    
    return {
        valid: missing.length === 0,
        missing,
        message: missing.length > 0 
            ? `Missing required properties: ${missing.join(', ')}`
            : 'All required properties are present'
    };
}

/**
 * Sanitizar string para uso en logs
 * @param {string} str - String a sanitizar
 * @param {number} maxLength - Longitud máxima
 * @returns {string} String sanitizado
 */
export function sanitizeForLog(str, maxLength = 100) {
    if (typeof str !== 'string') {
        str = String(str);
    }
    
    // Remover caracteres de control y limitar longitud
    const sanitized = str
        .replace(/[\x00-\x1F\x7F]/g, '') // Remover caracteres de control
        .substring(0, maxLength);
    
    return sanitized.length < str.length ? `${sanitized}...` : sanitized;
}

/**
 * Crear un logger simple con formato consistente
 * @param {string} service - Nombre del servicio
 * @returns {Object} Logger object
 */
export function createLogger(service) {
    const formatMessage = (level, message, data = null) => {
        const timestamp = DateTime.now().toISO();
        const logEntry = {
            timestamp,
            service,
            level: level.toUpperCase(),
            message: sanitizeForLog(message)
        };
        
        if (data) {
            logEntry.data = data;
        }
        
        return logEntry;
    };
    
    return {
        info: (message, data) => console.log(JSON.stringify(formatMessage('info', message, data))),
        warn: (message, data) => console.warn(JSON.stringify(formatMessage('warn', message, data))),
        error: (message, data) => console.error(JSON.stringify(formatMessage('error', message, data))),
        debug: (message, data) => {
            if (process.env.LOG_LEVEL === 'debug') {
                console.log(JSON.stringify(formatMessage('debug', message, data)));
            }
        }
    };
}

/**
 * Obtener estadísticas del sistema
 * @returns {Object} Estadísticas del sistema
 */
export function getSystemStats() {
    const memUsage = process.memoryUsage();
    
    return {
        timestamp: DateTime.now().toISO(),
        uptime: formatDuration(process.uptime() * 1000),
        memory: {
            rss: formatBytes(memUsage.rss),
            heapTotal: formatBytes(memUsage.heapTotal),
            heapUsed: formatBytes(memUsage.heapUsed),
            external: formatBytes(memUsage.external)
        },
        cpu: process.cpuUsage(),
        pid: process.pid,
        nodeVersion: process.version
    };
}
