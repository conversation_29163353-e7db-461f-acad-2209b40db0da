export enum Collections {
    Transfers = "transfers",
    Leads = "leads",
    Clients = "clients",
    Tags = "tags",
    Postbacks = "postbacks",
    Vendors = "vendors",
    BlacklistPhones = "blacklist_phones",
    FileProcessingTasks = "file_processing",
    DatabrokerQueues = "databroker_queues"
}


// export enum Collections {
//     Transfers = "transfers_test",
//     Leads = "leads_test",
//     Clients = "clients",
//     Tags = "tags",
//     Postbacks = "postbacks_test",
//     Vendors = "vendors",
//     BlacklistPhones = "blacklist_phones",
//     FileProcessingTasks = "file_processing_tasks_test",
//     DatabrokerQueues = "databroker_queues_test"
// }

export const getCollectionEnumByValue = (value: string): Collections | undefined => {
    return (Object.values(Collections) as string[]).includes(value)
        ? (value as Collections)
        : undefined;
}

