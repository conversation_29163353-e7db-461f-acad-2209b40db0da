import {Collections} from "./collections.enum";
import {mongoConnect} from "../database/mongoConnect.js";
import {docmqConfig} from "../config/docmq-config";

interface QueryOptions {
    sort?: Record<string, 1 | -1>;
    limit?: number;
    skip?: number;
}

export const findAllByFilter = async (
    collection: Collections,
    filters: any = {},
    options: QueryOptions = {}
) => {
    const {db} = await mongoConnect(docmqConfig.database.database);
    const query = db.collection(collection).find(filters);
    if (options.sort) query.sort(options.sort);
    if (options.skip !== undefined) query.skip(options.skip);
    if (options.limit !== undefined) query.limit(options.limit);
    return await query.toArray();
};
