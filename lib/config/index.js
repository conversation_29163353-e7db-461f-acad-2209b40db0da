/**
 * Configuración compartida para stream-docmq
 */

import { DateTime } from 'luxon';

/**
 * Configuración por defecto de la librería
 */
const defaultConfig = {
    // Configuración de base de datos
    database: {
        defaultTimeout: 30000,
        retryAttempts: 3,
        retryDelay: 1000,
        collections: {
            processingTasks: process.env.PROCESSING_TASKS_COLLECTION || 'file_processing_tasks',
            docmqJobs: process.env.COLLECTION_QUEUES_NAME || 'docmq_jobs',
            databrokerQueues: process.env.DATABROKER_QUEUES_COLLECTION || 'databroker_queues'
        }
    },

    // Configuración de logging
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        format: process.env.LOG_FORMAT || 'json',
        includeTimestamp: true,
        includeService: true
    },

    // Configuración de timeouts
    timeouts: {
        jobProcessing: parseInt(process.env.JOB_PROCESSING_TIMEOUT) || 900000, // 15 min
        connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT) || 30000,   // 30 sec
        gracefulShutdown: parseInt(process.env.GRACEFUL_SHUTDOWN_TIMEOUT) || 30000 // 30 sec
    },

    // Configuración de reintentos
    retry: {
        maxAttempts: parseInt(process.env.MAX_RETRY_ATTEMPTS) || 3,
        baseDelay: parseInt(process.env.RETRY_BASE_DELAY) || 1000,
        maxDelay: parseInt(process.env.RETRY_MAX_DELAY) || 10000,
        exponentialBackoff: process.env.RETRY_EXPONENTIAL_BACKOFF === 'true'
    },

    // Configuración de métricas
    metrics: {
        enabled: process.env.METRICS_ENABLED !== 'false',
        interval: parseInt(process.env.METRICS_INTERVAL) || 60000, // 1 min
        retention: parseInt(process.env.METRICS_RETENTION) || 86400000 // 24 hours
    }
};

/**
 * Clase para manejar la configuración de la librería
 */
class LibraryConfig {
    constructor() {
        this.config = { ...defaultConfig };
        this.loadEnvironmentOverrides();
    }

    /**
     * Cargar configuraciones desde variables de ambiente
     */
    loadEnvironmentOverrides() {
        // Aquí se pueden agregar más overrides específicos
        if (process.env.NODE_ENV === 'development') {
            this.config.logging.level = 'debug';
        }

        if (process.env.NODE_ENV === 'production') {
            this.config.logging.level = 'warn';
        }
    }

    /**
     * Obtener toda la configuración
     * @returns {Object} Configuración completa
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * Obtener configuración específica por clave
     * @param {string} key - Clave de configuración (ej: 'database.timeout')
     * @returns {any} Valor de configuración
     */
    get(key) {
        return key.split('.').reduce((obj, k) => obj?.[k], this.config);
    }

    /**
     * Establecer configuración específica
     * @param {string} key - Clave de configuración
     * @param {any} value - Valor a establecer
     */
    set(key, value) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, k) => {
            if (!obj[k]) obj[k] = {};
            return obj[k];
        }, this.config);
        target[lastKey] = value;
    }

    /**
     * Obtener información de la librería
     * @returns {Object} Información de la librería
     */
    getLibraryInfo() {
        return {
            name: '@stream-docmq/lib',
            version: '1.0.0',
            environment: process.env.NODE_ENV || 'development',
            timestamp: DateTime.now().toISO(),
            config: {
                database: this.config.database.collections,
                logging: this.config.logging,
                timeouts: this.config.timeouts
            }
        };
    }

    /**
     * Validar configuración
     * @returns {Object} Resultado de validación
     */
    validate() {
        const errors = [];
        const warnings = [];

        // Validar timeouts
        if (this.config.timeouts.jobProcessing < 60000) {
            warnings.push('Job processing timeout is less than 1 minute');
        }

        // Validar configuración de base de datos
        if (!this.config.database.collections.processingTasks) {
            errors.push('Processing tasks collection name is required');
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
}

// Instancia singleton de configuración
const config = new LibraryConfig();

export default config;
export { LibraryConfig, defaultConfig };
