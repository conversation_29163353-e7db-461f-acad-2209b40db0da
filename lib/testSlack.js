// xapp-1-A090UURU2J3-9045257158818-7649b61169b64490c69de1a71b72242ec2dd058c12e0b7fbe836711c5d01c022

import {SlackClient} from "./slack/index.js";
import {WebClient} from "@slack/web-api";

const test = async () => {

   const client = new WebClient('*******************************************************');
    const chanels = await client.conversations.list({
        exclude_archived: true,
        types: 'public_channel,private_channel'
    });
    console.log(chanels.channels);

    const slackClient = new SlackClient({
        token: '*******************************************************',
        defaultChannel: 'task-worker',
        loggerService: 'example-service'
    });
    const result = await slackClient.sendMessage(
        '¡Hola desde stream-docmq! 👋',
        {
            username: '<PERSON><PERSON><PERSON> Bo<PERSON>',
            iconEmoji: ':robot_face:'
        }
    );

    console.log('Mensaje enviado:', result.ts);
}

test().then(value => console.log(value)).catch(error => console.error(error));
