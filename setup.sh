#!/bin/bash

# 🚀 Stream-DocMQ Microservices Setup Script

echo "🌐 Stream-DocMQ Microservices Setup"
echo "===================================="

# Verificar Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js no está instalado. Por favor instala Node.js 18+ primero."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js versión 18+ requerida. Versión actual: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detectado"

# Verificar MongoDB
if ! command -v mongod &> /dev/null; then
    echo "⚠️  MongoDB no detectado. Asegúrate de que esté instalado y ejecutándose."
else
    echo "✅ MongoDB detectado"
fi

echo ""
echo "📦 Instalando dependencias en todos los microservicios independientes..."

# Instalar worker
echo "👷 Instalando worker..."
cd worker
if npm install; then
    echo "✅ Worker instalado"
else
    echo "❌ Error instalando worker"
    exit 1
fi

# Instalar producer
echo "📤 Instalando producer..."
cd ../producer
if npm install; then
    echo "✅ Producer instalado"
else
    echo "❌ Error instalando producer"
    exit 1
fi

# Instalar load-balancer
echo "🌐 Instalando load-balancer..."
cd ../load-balancer
if npm install; then
    echo "✅ Load-balancer instalado"
else
    echo "❌ Error instalando load-balancer"
    exit 1
fi

cd ..

echo ""
echo "🎉 ¡Setup completado exitosamente!"
echo ""
echo "🚀 Para iniciar el sistema:"
echo "   Terminal 1: npm run load-balancer"
echo "   Terminal 2: npm run producer"
echo "   Terminal 3: npm run worker"
echo "   Terminal 4: npm run worker  (opcional)"
echo ""
echo "📖 Para más información:"
echo "   - Worker: ./worker/README.md"
echo "   - Producer: ./producer/README.md"
echo "   - Load Balancer: ./load-balancer/README.md"
echo ""
echo "🔧 Configuración:"
echo "   - Worker: ./worker/docmq-config.js"
echo "   - Producer: ./producer/docmq-config.js"
echo "   - Load Balancer: ./load-balancer/docmq-config.js"
echo "   - MongoDB URI, concurrencia, etc."
