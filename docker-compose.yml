version: '3.8'

services:
  producer:
    build:
      context: ./producer
      dockerfile: Dockerfile
    container_name: queue-producer
    networks:
      - queue-network

  worker1:
    build:
      context: ./worker
      dockerfile: Dockerfile
    container_name: queue-worker1
    networks:
      - queue-network

  worker2:
    build:
      context: ./worker
      dockerfile: Dockerfile
    container_name: queue-worker2
    networks:
      - queue-network

  worker3:
    build:
      context: ./worker
      dockerfile: Dockerfile
    container_name: queue-worker3
    networks:
      - queue-network
networks:
  queue-network:
    driver: bridge
