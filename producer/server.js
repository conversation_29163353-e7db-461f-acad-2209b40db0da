import { Hono } from "hono";
import { cors } from "hono/cors";
import { logger } from "hono/logger";
import { prettyJSO<PERSON> } from "hono/pretty-json";
import { serve } from "@hono/node-server";
import os from 'os';

export class HttpServer {
    constructor(producer = null) {
        this.app = new Hono().basePath("/api");
        this.port = process.env.PORT || 3001;
        this.isRunning = false;
        this.server = null;
        this.producer = producer; // Referencia al producer para acceder a estadísticas

        this.setupMiddleware();
        this.setupRoutes();
    }

    setupMiddleware() {
        // CORS para permitir requests desde frontend
        this.app.use("/*", cors({
            origin: "*",
            allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            allowHeaders: ["Content-Type", "Authorization"],
        }));

        // Logger para requests
        this.app.use("/*", logger());

        // Pretty JSON para responses más legibles
        this.app.use("/*", prettyJSON());
    }

    setupRoutes() {
        // Health check básico
        this.app.get("/health", (c) => {
            return c.json({
                status: "ok",
                service: "stream-docmq-producer",
                version: process.env.VERSION || "1.0.0",
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || "development"
            });
        });

        // Información del producer
        this.app.get("/producer/info", (c) => {
            const producerInfo = {
                mode: this.producer?.mode || "unknown",
                isRunning: this.producer?.isRunning || false,
                jobsCreated: this.producer?.jobsCreated || 0,
                startTime: this.producer?.startTime || null,
                mongoUri: this.producer?.mongoUri ? "***configured***" : "not configured",
                databaseName: this.producer?.databaseName || "unknown",
                collectionName: this.producer?.collectionName || "unknown",
                serverInfo: {
                    hostname: os.hostname(),
                    platform: os.platform(),
                    cpus: os.cpus().length,
                    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024), // GB
                    freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024),   // GB
                    loadAverage: os.loadavg(),
                    nodeVersion: process.version,
                    pid: process.pid
                }
            };

            return c.json(producerInfo);
        });

        // Estadísticas del producer
        this.app.get("/producer/stats", (c) => {
            if (!this.producer) {
                return c.json({ error: "Producer not initialized" }, 503);
            }

            const uptime = this.producer.startTime ?
                Math.floor((Date.now() - this.producer.startTime) / 1000) : 0;
            const jobsPerMinute = uptime > 0 ?
                Math.round((this.producer.jobsCreated / uptime) * 60) : 0;

            const stats = {
                jobsCreated: this.producer.jobsCreated || 0,
                uptime: uptime,
                jobsPerMinute: jobsPerMinute,
                startTime: this.producer.startTime,
                mode: this.producer.mode,
                isRunning: this.producer.isRunning || false,
                changeStreamActive: this.producer.changeStream ? true : false
            };

            return c.json(stats);
        });

        // API para reencolar jobs (similar a checkAndFixOrphanedJobs)
        this.app.post("/jobs/requeue", async (c) => {
            if (!this.producer || !this.producer.db) {
                return c.json({error: "Producer not initialized"}, 503);
            }

            try {
                const body = await c.req.json().catch(() => ({}));
                const {
                    statuses = ['pending', 'processing'],
                    timeoutMinutes = 15,
                    dryRun = false
                } = body;

                const result = await this.requeueJobs(statuses, timeoutMinutes, dryRun);
                return c.json(result);

            } catch (error) {
                return c.json({
                    error: "Failed to requeue jobs",
                    message: error.message
                }, 500);
            }
        });

        // Lista de endpoints disponibles
        this.app.get("/", (c) => {
            const endpoints = {
                service: "stream-docmq-producer",
                version: process.env.VERSION || "1.0.0",
                endpoints: {
                    health: "GET /api/health",
                    producerInfo: "GET /api/producer/info",
                    producerStats: "GET /api/producer/stats",
                    producerStatus: "GET /api/producer/status",
                    metrics: "GET /api/metrics",
                    requeueJobs: "POST /api/jobs/requeue"
                },
                documentation: "https://github.com/your-username/stream-docmq"
            };

            return c.json(endpoints);
        });
    }

    async start() {
        if (this.isRunning) {
            console.warn("⚠️  HTTP server is already running");
            return;
        }

        try {
            this.isRunning = true;

            // Iniciar servidor HTTP usando @hono/node-server
            this.server = serve({
                fetch: this.app.fetch,
                port: this.port,
            });

            console.log(`🌐 Producer API server started at http://localhost:${this.port}/api`);
            console.log(`📊 Health check: http://localhost:${this.port}/api/health`);
            console.log(`📈 Producer stats: http://localhost:${this.port}/api/producer/stats`);
            console.log(`📋 All endpoints: http://localhost:${this.port}/api`);

            return {
                port: this.port,
                fetch: this.app.fetch,
                server: this.server
            };
        } catch (error) {
            console.error("❌ Failed to start HTTP server:", error);
            this.isRunning = false;
            throw error;
        }
    }

    async stop() {
        if (!this.isRunning) {
            console.warn("⚠️  HTTP server is not running");
            return;
        }

        try {
            if (this.server) {
                this.server.close();
                console.log("🛑 HTTP server stopped");
            }
            this.isRunning = false;
        } catch (error) {
            console.error("❌ Error stopping HTTP server:", error);
            throw error;
        }
    }

    setProducer(producer) {
        this.producer = producer;
    }

    getServerConfig() {
        return {
            port: this.port,
            fetch: this.app.fetch,
        };
    }


    async requeueJobs(statuses = ['pending', 'processing'], timeoutMinutes = 15, dryRun = false) {
        const startTime = Date.now();
        const processingTasksCollection = process.env.PROCESSING_TASKS_COLLECTION || 'file_processing_tasks';
        const docmqCollection = process.env.COLLECTION_QUEUES_NAME || 'docmq_jobs';

        console.log(`🔄 Iniciando requeue de jobs con estatus: [${statuses.join(', ')}]`);
        console.log(`⏱️  Timeout: ${timeoutMinutes} minutos`);
        console.log(`🧪 Modo: ${dryRun ? 'DRY RUN (solo simulación)' : 'EJECUCIÓN REAL'}`);

        try {
            // Importar DateTime aquí para evitar problemas de dependencias
            const { DateTime } = await import('luxon');

            // Calcular timestamp de corte
            const cutoffTime = DateTime.now().minus({ minutes: timeoutMinutes }).toISO();

            // Construir filtro de búsqueda
            const filter = {
                status: { $in: statuses }
            };

            // Si incluye 'processing', agregar filtro de tiempo
            if (statuses.includes('processing')) {
                filter.$or = [
                    { status: 'pending' },
                    {
                        status: 'processing',
                        processing_at: { $lt: cutoffTime }
                    }
                ];
                delete filter.status;
            }

            // Buscar jobs que cumplen los criterios
            const jobsToRequeue = await this.producer.db.collection(processingTasksCollection)
                .find(filter)
                .toArray();

            console.log(`📊 Jobs encontrados para requeue: ${jobsToRequeue.length}`);

            if (jobsToRequeue.length === 0) {
                return {
                    success: true,
                    message: 'No hay jobs para reencolar',
                    stats: {
                        found: 0,
                        requeued: 0,
                        errors: 0,
                        dryRun
                    },
                    processingTime: Date.now() - startTime
                };
            }

            let requeuedCount = 0;
            let errorCount = 0;
            const errors = [];
            const requeuedJobs = [];

            for (const job of jobsToRequeue) {
                try {
                    const jobId = job._id;
                    const currentStatus = job.status;

                    // Calcular tiempo en estado actual si es processing
                    let timeInStatus = null;
                    if (currentStatus === 'processing' && job.processing_at) {
                        const processingTime = DateTime.fromISO(job.processing_at);
                        timeInStatus = Math.round(DateTime.now().diff(processingTime, 'minutes').minutes);
                    }

                    // Verificar si existe job en DocMQ
                    const docmqJob = await this.producer.db.collection(docmqCollection).findOne({
                        ref: { $regex: `^doc_${jobId}_` }
                    });

                    const jobInfo = {
                        jobId: jobId.toString(),
                        currentStatus,
                        timeInStatus,
                        hasDocMQJob: !!docmqJob,
                        reprocessCount: (job.reprocess_count || 0) + 1
                    };

                    if (!dryRun) {
                        // Eliminar el job actual
                        await this.producer.db.collection(processingTasksCollection).deleteOne({ _id: jobId });

                        // Crear job limpio para reinsertar
                        const cleanJob = { ...job };
                        delete cleanJob.processing_at;
                        delete cleanJob.processed_by;
                        cleanJob.status = 'pending';
                        cleanJob.requeued_at = DateTime.utc().toISO();
                        cleanJob.requeue_count = (job.requeue_count || 0) + 1;
                        cleanJob.original_status = currentStatus;

                        // Reinsertar el job
                        await this.producer.db.collection(processingTasksCollection).insertOne(cleanJob);
                    }

                    requeuedJobs.push(jobInfo);
                    requeuedCount++;

                    console.log(`✅ Job ${dryRun ? 'marcado para' : ''} requeue: ${jobId} (${currentStatus}${timeInStatus ? `, ${timeInStatus}min` : ''}, DocMQ: ${docmqJob ? 'existe' : 'NO EXISTE'})`);

                } catch (error) {
                    errorCount++;
                    const errorInfo = {
                        jobId: job._id.toString(),
                        error: error.message
                    };
                    errors.push(errorInfo);
                    console.error(`❌ Error procesando job ${job._id}: ${error.message}`);
                }
            }

            const result = {
                success: true,
                message: `${dryRun ? 'Simulación completada' : 'Requeue completado'}`,
                stats: {
                    found: jobsToRequeue.length,
                    requeued: requeuedCount,
                    errors: errorCount,
                    dryRun
                },
                details: {
                    requeuedJobs,
                    errors: errors.length > 0 ? errors : undefined
                },
                processingTime: Date.now() - startTime
            };

            console.log(`🎯 Resultado: ${requeuedCount}/${jobsToRequeue.length} jobs ${dryRun ? 'marcados para requeue' : 'reencoleados'}, ${errorCount} errores`);

            return result;

        } catch (error) {
            console.error(`❌ Error en requeue de jobs: ${error.message}`);
            throw error;
        }
    }
}

export default HttpServer
