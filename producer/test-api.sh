#!/bin/bash

# 🧪 Script para probar la API del Producer

PORT=${PORT:-3001}
BASE_URL="http://localhost:$PORT/api"

echo "🧪 Testing Producer API at $BASE_URL"
echo "=================================="

# Función para hacer requests
test_endpoint() {
    local method=$1
    local endpoint=$2
    local description=$3
    
    echo ""
    echo "📋 Testing: $description"
    echo "🔗 $method $BASE_URL$endpoint"
    echo "---"
    
    if [ "$method" = "GET" ]; then
        curl -s -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint" | jq . 2>/dev/null || curl -s "$BASE_URL$endpoint"
    elif [ "$method" = "POST" ]; then
        curl -s -w "\n📊 Status: %{http_code} | Time: %{time_total}s\n" \
             -X POST \
             -H "Content-Type: application/json" \
             "$BASE_URL$endpoint" | jq . 2>/dev/null || curl -s -X POST "$BASE_URL$endpoint"
    fi
}

# Verificar si el servidor está corriendo
echo "🔍 Checking if server is running..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ Server is not running at $BASE_URL"
    echo "💡 Start the producer first: bun run start"
    exit 1
fi

echo "✅ Server is running!"

# Probar endpoints
test_endpoint "GET" "/" "API Documentation"
test_endpoint "GET" "/health" "Health Check"
test_endpoint "GET" "/producer/info" "Producer Information"
test_endpoint "GET" "/producer/stats" "Producer Statistics"
test_endpoint "GET" "/producer/status" "Producer Status (Combined)"
test_endpoint "GET" "/metrics" "Prometheus Metrics"

# Endpoints de control (si están implementados)
echo ""
echo "🎛️  Testing Control Endpoints:"
test_endpoint "POST" "/producer/start" "Start Producer"
test_endpoint "POST" "/producer/stop" "Stop Producer"

echo ""
echo "🎉 API Testing Complete!"
echo ""
echo "💡 Useful commands:"
echo "   Health check: curl $BASE_URL/health"
echo "   Producer stats: curl $BASE_URL/producer/stats | jq"
echo "   All endpoints: curl $BASE_URL/ | jq"
