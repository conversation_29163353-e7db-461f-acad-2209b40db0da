import axios from "axios";
import {docmqConfig} from "../docmq-config.js";

export const successCallbackTask = async (queueId, result) => {
    try {
        const url = `${docmqConfig.databroker.uri}/api/secure/webhook/completeQueue`;
        const body = {
            queueId: queueId,
            result
        }
        const xApiKey = docmqConfig.databroker.apiKey || ''
        await axios.post(url, body, {
            headers: {
                'x-api-key': xApiKey,
                'Content-Type': 'application/json'
            }
        });
    } catch (e) {
        console.error("Error on axios");
        console.error(e)
    }
}
