# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/docmq
MONGODB_DB_NAME=queue_system

# DocMQ Queue Configuration
COLLECTION_QUEUES_NAME=docmq_jobs
PROCESSING_TASKS_COLLECTION=file_processing_tasks

# Worker Configuration (used in queue config)
WORKER_COUNT=2

# Orphan Jobs Check (Producer only - evita conflictos entre múltiples workers)
# Verificación cada 10 min, solo jobs > 15 min se consideran huérfanos
ORPHAN_CHECK_INTERVAL=600000

# Trace Completion Monitoring (Producer only)
# Verificación cada 30 segundos para completar traces pendientes
TRACE_MONITOR_INTERVAL=30000
DATABROKER_QUEUES_COLLECTION=databroker_queues_test

# API Server Configuration
PORT=3001

# Logging (optional)
LOG_LEVEL=info
