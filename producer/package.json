{"name": "@stream-docmq/producer", "version": "1.0.0", "description": "Producer microservice that listens to MongoDB changes and creates DocMQ jobs", "main": "producer.js", "type": "module", "scripts": {"start": "bun producer.js", "dev": "bun producer.js --mode=dev", "prod": "bun producer.js --mode=prod", "test-api": "./test-api.sh", "api-health": "curl http://localhost:3001/api/health", "api-stats": "curl http://localhost:3001/api/producer/stats"}, "author": "Your Name", "license": "MIT", "dependencies": {"@hono/node-server": "^1.14.3", "chalk": "^5.0.0", "docmq": "^0.5.7", "dotenv": "^16.3.1", "hono": "^4.7.10", "lru-cache": "^10.0.0", "mongodb": "^6.0.0", "luxon": "^3.4.4", "axios": "^1.9.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/stream-docmq.git", "directory": "producer"}, "bugs": {"url": "https://github.com/your-username/stream-docmq/issues"}, "homepage": "https://github.com/your-username/stream-docmq/tree/main/producer#readme"}