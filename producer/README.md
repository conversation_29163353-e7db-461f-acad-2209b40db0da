# 📤 Producer Microservice

Microservicio producer que escucha cambios en MongoDB usando Change Streams y genera jobs automáticamente en DocMQ.

## 🚀 Características

- ✅ **Change Streams**: Escucha inserts en tiempo real en `file_processing_tasks`
- ✅ **Auto-generación**: Crea jobs automáticamente sin intervención manual
- ✅ **Modo Manual**: Para testing y desarrollo
- ✅ **Reconexión automática**: Manejo de desconexiones de MongoDB
- ✅ **Logs detallados**: Monitoreo completo de actividad

## 📦 Instalación

```bash
cd producer
npm install
```

## ⚙️ Configuración

El producer usa la configuración compartida en `../shared/docmq-config.js`. Configurar:

- **MongoDB URI**: Para conectar y escuchar cambios
- **Colección**: `file_processing_tasks` (configurable)
- **DocMQ Queue**: Cola donde crear los jobs

## 🚀 Uso

### **Modo Listener (Producción):**
```bash
npm start
# O explícitamente:
npm run listener
```

### **Modo Manual (Testing):**
```bash
npm run manual
```

### **Desarrollo:**
```bash
npm run dev
```

## 🔄 Funcionamiento

### **Modo Listener:**
1. **Conecta** a MongoDB
2. **Escucha** Change Streams en `file_processing_tasks`
3. **Detecta** nuevos inserts automáticamente
4. **Genera** job en DocMQ con el documento insertado
5. **Continúa** escuchando indefinidamente

### **Modo Manual:**
Comandos interactivos para testing:
```
add <task-name>     # Insertar documento en MongoDB
batch <count>       # Insertar múltiples documentos
random              # Insertar documento aleatorio
stats               # Mostrar estadísticas
quit                # Salir
```

## 📊 Estructura de Documentos

### **Input (MongoDB):**
```javascript
// Documento insertado en file_processing_tasks
{
  "_id": ObjectId("..."),
  "filePath": "/uploads/document.pdf",
  "userId": 123,
  "status": "pending",
  "createdAt": ISODate("..."),
  "metadata": {
    "size": "2MB",
    "type": "pdf"
  }
}
```

### **Output (DocMQ Job):**
```javascript
// Job generado automáticamente
{
  "ref": "file-task-507f1f77bcf86cd799439011",
  "payload": {
    "documentId": "507f1f77bcf86cd799439011",
    "filePath": "/uploads/document.pdf",
    "userId": 123,
    "metadata": {
      "size": "2MB", 
      "type": "pdf"
    },
    "createdAt": "2025-05-27T08:15:00Z"
  },
  "runEvery": null,
  "retries": 3
}
```

## 🔧 Variables de Entorno

```bash
# MongoDB
MONGODB_URI=mongodb://localhost:27017/docmq
MONGODB_DB=queue_system
MONGODB_COLLECTION=file_processing_tasks

# DocMQ
DOCMQ_QUEUE_NAME=file_processing_queue
DOCMQ_RETRIES=3
```

## 🐳 Docker

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
CMD ["npm", "start"]
```

## 📈 Escalabilidad

### **Múltiples Producers:**
```bash
# Producer 1 (Principal)
npm start

# Producer 2 (Backup) - Solo si el principal falla
# Change Streams garantizan que solo uno procese cada insert
```

**Nota:** MongoDB Change Streams garantizan que cada insert se procese solo una vez, incluso con múltiples producers.

## 🚨 Troubleshooting

### **No detecta inserts:**
1. Verificar que MongoDB soporte Change Streams (Replica Set requerido)
2. Comprobar permisos de lectura en la colección
3. Verificar que la colección existe

### **Jobs duplicados:**
1. Verificar que solo hay un producer activo
2. Comprobar configuración de DocMQ
3. Revisar logs de generación de jobs

### **Desconexiones frecuentes:**
1. Verificar estabilidad de conexión a MongoDB
2. Aumentar timeout de conexión
3. Implementar retry logic

## 📝 Logs

### **Modo Listener:**
```
📤 PRODUCER/LISTENER INICIADO
=====================================
🗄️  Base de datos: queue_system
📋 Colección: file_processing_tasks
🔄 Cola DocMQ: file_processing_queue
⏱️  Iniciado: 5/27/2025, 2:15:00 AM

📥 Nuevo insert detectado: 507f1f77bcf86cd799439011
📤 Job creado: file-task-507f1f77bcf86cd799439011
✅ Job encolado exitosamente en DocMQ

📊 ESTADÍSTICAS:
   📥 Inserts detectados: 156
   📤 Jobs creados: 156
   ❌ Errores: 0
   ⏱️  Uptime: 1800s
```

### **Modo Manual:**
```
📤 PRODUCER MANUAL
==================
Comandos disponibles:
  add <task>    - Insertar tarea
  batch <num>   - Insertar múltiples
  random        - Insertar aleatorio
  stats         - Estadísticas
  quit          - Salir

> add procesar-video
✅ Documento insertado: 507f1f77bcf86cd799439011
📤 Job generado automáticamente

> stats
📊 Documentos insertados: 1
📤 Jobs generados: 1
```

## 🔗 Dependencias

- **DocMQ**: Sistema de colas
- **MongoDB**: Base de datos y Change Streams
- **Chalk**: Logs con colores
- **Shared**: Configuraciones compartidas

## 🔄 Integración

El producer se integra automáticamente con:

1. **Workers**: Los jobs creados son procesados por workers
2. **Load Balancer**: Los workers reportan estadísticas de jobs procesados
3. **MongoDB**: Escucha cambios y almacena estado
