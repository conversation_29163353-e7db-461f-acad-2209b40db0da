# 🌐 Stream-DocMQ Independent Microservices

Sistema de colas distribuido basado en **microservicios completamente independientes** con load balancing inteligente, usando DocMQ y MongoDB para procesar tareas de larga duración (2-3 minutos).

## 🏗️ Arquitectura de Microservicios Independientes

```
stream-docmq/
├── worker/           # 👷 Worker Microservice (100% independiente)
├── producer/         # 📤 Producer Microservice (100% independiente)
├── load-balancer/    # 🌐 Load Balancer Microservice (100% independiente)
└── archive/          # 📁 Legacy Files
```

## 🚀 Características

- ✅ **Microservicios independientes**: Cada componente es deployable por separado
- ✅ **Load Balancer Distribuido**: Monitoreo y balanceo inteligente entre múltiples instancias
- ✅ **Workers Load-Balanced**: Se registran automáticamente con el load balancer
- ✅ **Producer Automático**: Escucha inserts en MongoDB y genera jobs automáticamente
- ✅ **Deduplicación Distribuida**: Previene procesamiento duplicado entre instancias
- ✅ **Monitoreo en Tiempo Real**: Health checks, métricas y rebalancing automático
- ✅ **Fault Tolerance**: Detección de instancias caídas y recuperación automática
- ✅ **Containerización**: Cada microservicio con su propio Dockerfile

## 🏗️ Arquitectura

```
🌐 LOAD BALANCER DISTRIBUIDO
┌─────────────────────────────────────────────────────────────┐
│                 LOAD BALANCER                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Instancia 1 │ │ Instancia 2 │ │ Instancia 3 │          │
│  │Worker(12)   │ │Worker(16)   │ │Worker(8)    │          │
│  │Server-A     │ │Server-B     │ │Server-C     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 MONGODB DISTRIBUIDO                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ docmq_jobs  │ │instance_stats│ │processed_jobs│         │
│  │   (Queue)   │ │ (Metrics)   │ │ (Dedup)     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 📦 Instalación

```bash
npm install
```

## ⚙️ Configuración

El sistema está configurado para usar MongoDB. Actualiza la URI en `docmq-config.js` si es necesario.

## 📦 Instalación Independiente

Cada microservicio se instala por separado:

```bash
# Worker Microservice
cd worker && npm install

# Producer Microservice
cd producer && npm install

# Load Balancer Microservice
cd load-balancer && npm install
```

## 🚀 Uso por Microservicio

### **🌐 Load Balancer:**
```bash
cd load-balancer
npm start
```

### **📤 Producer:**
```bash
cd producer
npm start
```

### **👷 Worker (Instancia 1):**
```bash
cd worker
npm start
```

### **👷 Worker (Instancia 2):**
```bash
cd worker
npm start
```

## 🚀 Uso Rápido (Sistema Completo)

```bash
# Terminal 1: Load Balancer
cd load-balancer && npm start

# Terminal 2: Producer
cd producer && npm start

# Terminal 3: Worker 1
cd worker && npm start

# Terminal 4: Worker 2 (opcional)
cd worker && npm start
```

## 📊 Microservicios

### **👷 Worker** (`./worker/`)
- **Función**: Procesa jobs de DocMQ con load balancing automático
- **Scripts**: `npm start`, `npm run dev`, `npm run prod`
- **Puerto**: N/A (worker sin API)

### **📤 Producer** (`./producer/`)
- **Función**: Escucha MongoDB y genera jobs automáticamente
- **Scripts**: `npm start`, `npm run manual`, `npm run listener`
- **Puerto**: N/A (listener de MongoDB)

### **🌐 Load Balancer** (`./load-balancer/`)
- **Función**: Monitorea workers y genera recomendaciones
- **Scripts**: `npm start`, `npm run dev`, `npm run monitor`
- **Puerto**: N/A (monitor interno)

## 📁 Estructura Detallada

```
stream-docmq/
├── worker/                        # 👷 Worker Microservice
│   ├── package.json
│   ├── README.md
│   ├── load-balanced-worker.js
│   ├── docmq-config.js           # ✅ Independiente
│   ├── mongodb-deduplication.js  # ✅ Independiente
│   └── mongoConnect.js           # ✅ Independiente
├── producer/                      # 📤 Producer Microservice
│   ├── package.json
│   ├── README.md
│   ├── producer.js
│   ├── docmq-config.js           # ✅ Independiente
│   ├── mongodb-deduplication.js  # ✅ Independiente
│   └── mongoConnect.js           # ✅ Independiente
├── load-balancer/                 # 🌐 Load Balancer Microservice
│   ├── package.json
│   ├── README.md
│   ├── distributed-load-balancer.js
│   ├── docmq-config.js           # ✅ Independiente
│   ├── mongodb-deduplication.js  # ✅ Independiente
│   └── mongoConnect.js           # ✅ Independiente
└── archive/
    └── [archivos legacy]
```



## 📊 Monitoreo en Tiempo Real

### **Load Balancer muestra:**
```
📊 ESTADO ACTUAL DEL LOAD BALANCER
----------------------------------------
🌐 Instancias activas: 3
🔄 Concurrencia total: 36
📊 Jobs activos: 12
📈 Jobs procesados: 156
⚖️  Utilización promedio: 33.3%

📋 INSTANCIAS:
   1. 🟢 lb-worker-server-1
      Concurrencia: 12, Activos: 4
      Utilización: 33.3%, Jobs/min: 18
```

### **Workers muestran:**
```
📊 ESTADÍSTICAS DEL WORKER LOAD-BALANCED
----------------------------------------
📊 Jobs procesados: 45
⚠️  Jobs saltados: 3
⏱️  Uptime: 300s
⚡ Jobs/minuto: 9
🔄 Tareas activas: 4/12
📈 Utilización: 33.3%
🌐 Registrado con Load Balancer: ✅
```

## 🌐 Uso en Múltiples Servidores

### **Servidor 1:**
```bash
npm run load-balancer  # Load balancer central
npm run producer       # Producer
npm run lb-worker      # Worker local
```

### **Servidor 2:**
```bash
npm run lb-worker      # Solo worker
```

### **Servidor 3:**
```bash
npm run lb-worker      # Solo worker
```

## 🎯 Características del Load Balancing

### **✅ Automático:**
- Workers se registran automáticamente
- Distribución natural basada en capacidad
- No requiere configuración manual

### **📊 Inteligente:**
- Monitorea utilización en tiempo real
- Detecta instancias sobrecargadas/subutilizadas
- Genera recomendaciones automáticas

### **🛡️ Fault Tolerant:**
- Detecta instancias caídas automáticamente
- Health checks cada 30 segundos
- Rebalancing cada 2 minutos

## 🔧 Para Tareas de 2-3 Minutos

Este sistema está **optimizado para tareas largas**:

- ✅ **Deduplicación distribuida** previene reprocesamiento
- ✅ **Load balancing inteligente** evita desequilibrios
- ✅ **Monitoreo en tiempo real** para optimización
- ✅ **Fault tolerance** para alta disponibilidad

## 🚨 Troubleshooting

1. **Verificar MongoDB**: Debe estar ejecutándose y accesible
2. **Revisar configuración**: `docmq-config.js` con URI correcta
3. **Verificar logs**: Cada componente muestra logs detallados
4. **Health checks**: Load balancer reporta estado de instancias

## 📈 Escalabilidad

### **Horizontal (Más Servidores):**
```bash
# Agregar nueva instancia es tan simple como:
npm run lb-worker
# Se registra automáticamente con el load balancer
```

### **Vertical (Más Concurrencia):**
```bash
# Editar load-balanced-worker.js línea 18:
this.concurrency = 20  # Aumentar concurrencia
```

**¡El sistema se balancea automáticamente sin configuración adicional!** 🎯
